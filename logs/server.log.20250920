2025-09-20 13:18:29.038[36m [info] [unknown] [once.go:78] [0mRedis客户端初始化成功
2025-09-20 13:18:29.038[36m [info] [main.go:100] [0mpprof服务已禁用
2025-09-20 13:18:29.039[36m [info] [udp_server.go:61] [0mUDP服务器启动在 0.0.0.0:8990
2025-09-20 13:18:29.039[36m [info] [app.go:239] [0mmanager-api service initialized successfully
2025-09-20 13:18:29.039[36m [info] [app.go:51] [0m设备状态管理器初始化成功
2025-09-20 13:18:29.039[36m [info] [service.go:71] [0mStarting Manager-API service...
2025-09-20 13:18:29.039[36m [info] [main.go:116] [0m服务器已启动，按 Ctrl+C 退出
2025-09-20 13:18:29.039[36m [info] [local_manager.go:43] [0m本地MCP管理器默认工具初始化完成
2025-09-20 13:18:29.039[36m [info] [manager.go:47] [0m=== 启动MCP管理器集群 ===
2025-09-20 13:18:29.039[36m [info] [manager.go:50] [0m启动本地MCP管理器...
2025-09-20 13:18:29.039[36m [info] [local_manager.go:172] [0m本地MCP管理器已启动
2025-09-20 13:18:29.039[36m [info] [manager.go:57] [0m启动全局MCP管理器...
2025-09-20 13:18:29.039[36m [info] [config_checker.go:14] [0m=== MCP配置检查 ===
2025-09-20 13:18:29.039[36m [info] [config_checker.go:18] [0m全局MCP启用状态: true
2025-09-20 13:18:29.039[36m [info] [config_checker.go:28] [0m重连配置: 间隔=300秒, 最大尝试次数=10
2025-09-20 13:18:29.039[36m [info] [config_checker.go:42] [0m共配置了 2 个MCP服务器:
2025-09-20 13:18:29.039[36m [info] [mqtt_server.go:80] [0mMQTT 服务器启动，监听 0.0.0.0:2883 地址...
2025-09-20 13:18:29.039[36m [info] [config_checker.go:82] [0m  [1] ❌ filesystem (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:18:29.039[36m [info] [config_checker.go:82] [0m  [2] ❌ memory (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:18:29.039[36m [info] [config_checker.go:87] [0m配置检查完成: 2个服务器已启用, 2个存在问题
2025-09-20 13:18:29.039[33m [warning] [config_checker.go:90] [0m⚠️  发现配置问题，请检查上述错误并修复
2025-09-20 13:18:29.040[36m [info] [config_checker.go:101] [0m--- 设备MCP配置检查 ---
2025-09-20 13:18:29.040[36m [info] [config_checker.go:104] [0m设备MCP启用状态: true
2025-09-20 13:18:29.040[36m [info] [config_checker.go:114] [0mWebSocket路径: /xiaozhi/mcp/
2025-09-20 13:18:29.040[36m [info] [config_checker.go:115] [0m每设备最大连接数: 5
2025-09-20 13:18:29.040[36m [info] [config_checker.go:96] [0m=== MCP配置检查完成 ===
2025-09-20 13:18:29.040[36m [info] [global_manage.go:103] [0m从配置中读取到 2 个MCP服务器配置
2025-09-20 13:18:29.040[36m [info] [global_manage.go:107] [0mMCP服务器[1]: Type=sse, Name=filesystem, Url=http://localhost:3001/sse, SSEUrl=, Enabled=true
2025-09-20 13:18:29.040[36m [info] [global_manage.go:107] [0mMCP服务器[2]: Type=streamablehttp, Name=memory, Url=http://localhost:3002/mcp, SSEUrl=, Enabled=true
2025-09-20 13:18:29.040[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: filesystem (URL: )
2025-09-20 13:18:29.040[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: filesystem, SSE URL: 
2025-09-20 13:18:29.040[31m [error] [global_manage.go:233] [0m启动MCP客户端失败，服务器: filesystem, 错误: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:18:29.040[31m [error] [global_manage.go:116] [0m连接到MCP服务器 filesystem 失败: 连接MCP服务器失败: 启动客户端失败: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:18:29.040[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: memory (URL: )
2025-09-20 13:18:29.040[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: memory, SSE URL: 
2025-09-20 13:18:29.040[36m [info] [global_manage.go:237] [0mMCP客户端启动成功: memory
2025-09-20 13:18:29.040[36m [info] [global_manage.go:253] [0m正在初始化MCP服务器: memory
2025-09-20 13:18:29.041[31m [error] [global_manage.go:256] [0m初始化MCP服务器失败，服务器: memory, 错误: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:18:29.041[31m [error] [global_manage.go:116] [0m连接到MCP服务器 memory 失败: 连接MCP服务器失败: 初始化失败: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:18:29.041[36m [info] [global_manage.go:126] [0m成功连接了 0 个MCP服务器
2025-09-20 13:18:29.041[36m [info] [global_manage.go:131] [0m全局MCP管理器已启动
2025-09-20 13:18:29.041[36m [info] [manager.go:64] [0m设备MCP管理器将根据连接动态创建
2025-09-20 13:18:29.041[36m [info] [manager.go:67] [0m=== MCP管理器集群启动完成 ===
2025-09-20 13:18:29.041[36m [info] [manager.go:130] [0mMCP管理器启动统计:
2025-09-20 13:18:29.041[36m [info] [manager.go:131] [0m  - 本地工具数量: 0
2025-09-20 13:18:29.041[36m [info] [manager.go:132] [0m  - 全局工具数量: 0
2025-09-20 13:18:29.041[36m [info] [manager.go:133] [0m  - 设备管理器: 动态管理
2025-09-20 13:18:29.041[36m [info] [manager.go:134] [0m  - 总计工具数量: 0
2025-09-20 13:18:29.041[36m [info] [websocket_server.go:103] [0mWebSocket 服务器启动在 ws://0.0.0.0:8989/xiaozhi/v1/
2025-09-20 13:18:29.041[36m [info] [websocket_server.go:104] [0mMCP WebSocket 端点: ws://0.0.0.0:8989/xiaozhi/mcp/{deviceId}
2025-09-20 13:18:29.041[36m [info] [websocket_server.go:105] [0mMCP API 端点: http://0.0.0.0:8989/xiaozhi/api/mcp/tools/{deviceId}
2025-09-20 13:18:29.073[36m [info] [service.go:77] [0mInitial health check passed
2025-09-20 13:18:29.073[37m [debug] [service.go:383] [0mHealth checker started
2025-09-20 13:18:29.073[37m [debug] [service.go:413] [0mChat history flusher started
2025-09-20 13:18:29.073[36m [info] [service.go:85] [0mManager-API service started successfully
2025-09-20 13:18:29.073[36m [info] [app.go:67] [0mmanager-api service started successfully
2025-09-20 13:18:30.039[36m [info] [local_mcp_tool.go:28] [0m初始化聊天相关的本地MCP工具...
2025-09-20 13:18:30.039[36m [info] [mqtt_udp_adapter.go:74] [0mMqttUdpAdapter开始启动，尝试连接MQTT服务器...
2025-09-20 13:18:30.039[36m [info] [mqtt_udp_adapter.go:75] [0mMQTT配置: Broker=%s:%d, ClientID=%s, Username=%s127.0.0.12883xiaozhi_serveradmin
2025-09-20 13:18:30.040[36m [info] [mqtt_udp_adapter.go:88] [0mMQTT已连接
2025-09-20 13:18:30.040[36m [info] [device_hook.go:106] [0m=== 收到订阅包 ===
2025-09-20 13:18:30.040[36m [info] [device_hook.go:107] [0m客户端ID: xiaozhi_server
2025-09-20 13:18:30.040[36m [info] [device_hook.go:108] [0m包类型: 8
2025-09-20 13:18:30.040[36m [info] [device_hook.go:109] [0m包ID: 1
2025-09-20 13:18:30.040[36m [info] [device_hook.go:112] [0m订阅信息:
2025-09-20 13:18:30.040[36m [info] [device_hook.go:114] [0m  1. 主题: /p2p/device_public/#, QoS: 0
2025-09-20 13:18:30.040[36m [info] [device_hook.go:118] [0m==================
2025-09-20 13:18:30.043[36m [info] [local_manager.go:69] [0m成功注册本地工具: exit_conversation - 仅当用户明确表达想要结束对话的意图时使用，例如说"再见"、"退出"、"结束对话"等告别词汇。不要因为用户提问身份、询问功能或进行正常对话而调用此工具
2025-09-20 13:18:30.047[36m [info] [local_manager.go:69] [0m成功注册本地工具: clear_conversation_history - 当用户要求清空、清除或重置历史对话记录时使用，用于清空当前会话的所有历史对话内容
2025-09-20 13:18:30.050[36m [info] [local_manager.go:69] [0m成功注册本地工具: play_music - 播放播客，音乐，电台等音频内容，涉及播放的都可以调用，可以传入内容作者或者名称，如果是随机播放或者播放下一首，传入*即可。参数格式: {\"name\": \"名称\"}
2025-09-20 13:18:30.050[36m [info] [local_mcp_tool.go:73] [0m聊天相关的本地MCP工具初始化完成
2025-09-20 13:18:30.050[36m [info] [app.go:181] [0m聊天相关的本地MCP工具注册完成
2025-09-20 13:18:59.302[36m [info] [websocket_server.go:158] [0m收到连接请求，设备ID: 42:32:13:b2:7b:f2, ClientID:web_test_client
2025-09-20 13:18:59.302[36m [info] [unknown] [base.go:36] [0mRedis用户配置提供者初始化成功
2025-09-20 13:18:59.321[37m [debug] [service.go:193] [0mGetDeviceConfig config: &{DeviceMaxOutputSize:0 ChatHistoryConf:2 Plugins:map[get_news_from_newsnow:{"url": "https://newsnow.busiyi.world/api/s?id=", "news_sources": "澎湃新闻;百度热搜;财联社"} get_weather:{"api_key": "a861d0d5e7bf4ee1a83d9a9e4f96d4da", "api_host": "mj7p3y7naa.re.qweatherapi.com", "default_location": "广州"} play_music:{}] McpEndpoint: Voiceprint:<nil> SelectedModule:map[ASR:ASR_DoubaoStreamASR Intent:Intent_function_call LLM:LLM_AliLLM Memory:Memory_mem0ai TTS:TTS_DoubaoTTS VAD:VAD_SileroVAD VLLM:VLLM_ChatGLMVLLM] Prompt:[角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话 Memory:map[Memory_mem0ai:map[api_key:你的api_key type:mem0ai]] VAD:map[VAD_SileroVAD:map[min_silence_duration_ms:700 model_dir:models/snakers4_silero-vad threshold:0.5 type:silero]] ASR:map[ASR_DoubaoStreamASR:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]] LLM:map[LLM_AliLLM:map[api_key:sk-e17e481f8e90416e861d7f095cb7534d base_url:https://dashscope.aliyuncs.com/compatible-mode/v1 frequency_penalty:0 max_tokens:500 model_name:qwen2.5-72b-instruct temperature:0.8 top_k:50 top_p:1 type:openai]] VLLM:map[VLLM_ChatGLMVLLM:map[api_key:你的api_key base_url:https://open.bigmodel.cn/api/paas/v4/ model_name:glm-4v-flash type:openai]] TTS:map[TTS_DoubaoTTS:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:]] Intent:map[Intent_function_call:map[type:function_call]]}
2025-09-20 13:18:59.321[37m [debug] [service.go:201] [0mDevice configuration retrieved and cached for 42:32:13:b2:7b:f2
2025-09-20 13:18:59.321[36m [info] [chat.go:228] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置: {"device_max_output_size":"0","chat_history_conf":2,"plugins":{"get_news_from_newsnow":"{\"url\": \"https://newsnow.busiyi.world/api/s?id=\", \"news_sources\": \"澎湃新闻;百度热搜;财联社\"}","get_weather":"{\"api_key\": \"a861d0d5e7bf4ee1a83d9a9e4f96d4da\", \"api_host\": \"mj7p3y7naa.re.qweatherapi.com\", \"default_location\": \"广州\"}","play_music":"{}"},"selected_module":{"ASR":"ASR_DoubaoStreamASR","Intent":"Intent_function_call","LLM":"LLM_AliLLM","Memory":"Memory_mem0ai","TTS":"TTS_DoubaoTTS","VAD":"VAD_SileroVAD","VLLM":"VLLM_ChatGLMVLLM"},"prompt":"[角色设定]\n你是小云，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话","Memory":{"Memory_mem0ai":{"api_key":"你的api_key","type":"mem0ai"}},"VAD":{"VAD_SileroVAD":{"min_silence_duration_ms":700,"model_dir":"models/snakers4_silero-vad","threshold":0.5,"type":"silero"}},"ASR":{"ASR_DoubaoStreamASR":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","appid":"**********","boosting_table_name":"","cluster":"volcengine_input_common","correct_table_name":"","output_dir":"tmp/","type":"doubao_stream"}},"LLM":{"LLM_AliLLM":{"api_key":"sk-e17e481f8e90416e861d7f095cb7534d","base_url":"https://dashscope.aliyuncs.com/compatible-mode/v1","frequency_penalty":"0","max_tokens":"500","model_name":"qwen2.5-72b-instruct","temperature":"0.8","top_k":"50","top_p":"1","type":"openai"}},"VLLM":{"VLLM_ChatGLMVLLM":{"api_key":"你的api_key","base_url":"https://open.bigmodel.cn/api/paas/v4/","model_name":"glm-4v-flash","type":"openai"}},"TTS":{"TTS_DoubaoTTS":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","api_url":"https://openspeech.bytedance.com/api/v1/tts","appid":"**********","authorization":"Bearer;","cluster":"volcano_tts","output_dir":"tmp/","pitch_ratio":"","private_voice":"BV051_streaming","speed_ratio":"1.5","type":"doubao","voice":"BV001_streaming","volume_ratio":""}},"Intent":{"Intent_function_call":{"type":"function_call"}}}
2025-09-20 13:18:59.321[36m [info] [chat.go:278] [0m设备 42:32:13:b2:7b:f2 配置转换完成: VAD=webrtc_vad, ASR=doubao, LLM=LLM_AliLLM, TTS=doubao_ws TTS-CONFIG:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:] Memory=long_term
2025-09-20 13:18:59.321[36m [info] [chat.go:234] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置
2025-09-20 13:18:59.321[36m [info] [unknown] [factory.go:41] [0m短记忆创建成功: short_term_redis
2025-09-20 13:18:59.321[36m [info] [unknown] [long_term.go:34] [0m正在创建长记忆提供者: memobase
2025-09-20 13:18:59.327[36m [info] [unknown] [memobase_provider.go:79] [0mMemobase客户端初始化完成: http://xiaozhi.localhost:8777
2025-09-20 13:18:59.327[36m [info] [unknown] [factory.go:35] [0mMemobase提供者初始化成功: http://xiaozhi.localhost:8777
2025-09-20 13:18:59.328[36m [info] [unknown] [long_term.go:40] [0m长记忆管理器已启动，使用提供者: memobase
2025-09-20 13:18:59.328[36m [info] [unknown] [factory.go:46] [0m长记忆创建成功: long_term_memobase
2025-09-20 13:18:59.328[33m [warning] [unknown] [memory.go:144] [0m长记忆不支持直接获取历史消息
2025-09-20 13:18:59.328[36m [info] [chat.go:133] [0m为设备 42:32:13:b2:7b:f2 从全局记忆加载了 0 条历史消息
2025-09-20 13:18:59.328[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:18:59.333[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:18:59.340[36m [info] [chat.go:141] [0m为设备 42:32:13:b2:7b:f2 在会话初始化时获取到用户画像（全局配置），长度: 138 字符
2025-09-20 13:18:59.354[36m [info] [client.go:406] [0msaveMemory response body: {"code":0,"msg":"success","data":null}
2025-09-20 13:18:59.354[37m [debug] [service.go:262] [0mMemory saved for device 42:32:13:b2:7b:f2
2025-09-20 13:18:59.354[36m [info] [device_status_manager.go:55] [0m设备 42:32:13:b2:7b:f2 开始新会话，固件版本: unknown
2025-09-20 13:18:59.354[36m [info] [chat.go:165] [0m设备 42:32:13:b2:7b:f2 开始状态跟踪，固件版本: unknown
2025-09-20 13:18:59.355[37m [debug] [eino_llm.go:165] [0mopenaiConfig: &{APIKey:sk-e17e481f8e90416e861d7f095cb7534d Timeout:0s HTTPClient:<nil> ByAzure:false AzureModelMapperFunc:<nil> BaseURL:https://dashscope.aliyuncs.com/compatible-mode/v1 APIVersion: Model:qwen2.5-72b-instruct MaxTokens:0xc000136390 Temperature:<nil> TopP:<nil> Stop:[] PresencePenalty:<nil> ResponseFormat:<nil> Seed:<nil> FrequencyPenalty:<nil> LogitBias:map[] User:<nil> ExtraFields:map[]}
2025-09-20 13:18:59.355[36m [info] [eino_llm.go:173] [0m成功创建OpenAI ChatModel，模型: qwen2.5-72b-instruct
2025-09-20 13:18:59.355[37m [debug] [registry.go:175] [0m函数 'get_weather' 注册成功
2025-09-20 13:18:59.355[37m [debug] [registry.go:175] [0m函数 'get_news_from_newsnow' 注册成功
2025-09-20 13:18:59.355[36m [info] [client.go:325] [0m初始化asr, asrConfig: {Provider:doubao Config:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]}
2025-09-20 13:18:59.355[37m [debug] [session.go:593] [0mprocessChatText start
2025-09-20 13:18:59.355[36m [info] [session.go:137] [0m收到文本消息: {"type":"hello","device_id":"42:32:13:b2:7b:f2","device_name":"Web测试设备","device_mac":"42:32:13:b2:7b:f2","token":"your-token1","features":{"mcp":true}}
2025-09-20 13:19:09.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:09.042[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:14.356[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:19:19.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:19.044[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:29.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:29.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:39.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:39.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:49.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:49.044[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:59.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:19:59.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:19:59.356[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:20:09.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:09.044[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:14.357[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:20:19.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:19.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:29.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:29.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:39.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:39.044[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:49.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:49.044[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:59.040[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:20:59.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:20:59.355[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:21:09.039[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:21:09.043[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:21:14.357[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:21:16.674[36m [info] [main.go:119] [0m正在关闭服务器...
2025-09-20 13:21:16.674[36m [info] [app.go:250] [0m开始关闭应用服务...
2025-09-20 13:21:16.674[36m [info] [app.go:254] [0m关闭manager-api服务...
2025-09-20 13:21:16.674[36m [info] [service.go:98] [0mStopping Manager-API service...
2025-09-20 13:21:16.674[36m [info] [service.go:125] [0mAll workers stopped gracefully
2025-09-20 13:21:16.674[36m [info] [service.go:131] [0mManager-API service stopped
2025-09-20 13:21:16.674[36m [info] [app.go:258] [0mmanager-api服务已关闭
2025-09-20 13:21:16.674[36m [info] [app.go:271] [0m执行全局资源清理...
2025-09-20 13:21:16.674[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:21:16.674[36m [info] [app.go:276] [0m全局资源清理完成
2025-09-20 13:21:16.674[36m [info] [app.go:279] [0m应用服务关闭完成
2025-09-20 13:21:16.674[36m [info] [main.go:130] [0m服务器已关闭
2025-09-20 13:21:16.706[36m [info] [main.go:57] [0m程序退出: 执行最终的全局资源清理（备用机制）...
2025-09-20 13:21:16.706[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:21:16.706[36m [info] [main.go:61] [0m程序退出: 最终全局资源清理完成
2025-09-20 13:21:19.571[36m [info] [unknown] [once.go:78] [0mRedis客户端初始化成功
2025-09-20 13:21:19.571[36m [info] [main.go:100] [0mpprof服务已禁用
2025-09-20 13:21:19.572[36m [info] [udp_server.go:61] [0mUDP服务器启动在 0.0.0.0:8990
2025-09-20 13:21:19.572[36m [info] [app.go:239] [0mmanager-api service initialized successfully
2025-09-20 13:21:19.572[36m [info] [app.go:51] [0m设备状态管理器初始化成功
2025-09-20 13:21:19.572[36m [info] [service.go:71] [0mStarting Manager-API service...
2025-09-20 13:21:19.572[36m [info] [local_manager.go:43] [0m本地MCP管理器默认工具初始化完成
2025-09-20 13:21:19.572[36m [info] [manager.go:47] [0m=== 启动MCP管理器集群 ===
2025-09-20 13:21:19.572[36m [info] [manager.go:50] [0m启动本地MCP管理器...
2025-09-20 13:21:19.572[36m [info] [local_manager.go:172] [0m本地MCP管理器已启动
2025-09-20 13:21:19.572[36m [info] [manager.go:57] [0m启动全局MCP管理器...
2025-09-20 13:21:19.572[36m [info] [main.go:116] [0m服务器已启动，按 Ctrl+C 退出
2025-09-20 13:21:19.572[36m [info] [config_checker.go:14] [0m=== MCP配置检查 ===
2025-09-20 13:21:19.572[36m [info] [config_checker.go:18] [0m全局MCP启用状态: true
2025-09-20 13:21:19.572[36m [info] [config_checker.go:28] [0m重连配置: 间隔=300秒, 最大尝试次数=10
2025-09-20 13:21:19.572[36m [info] [mqtt_server.go:80] [0mMQTT 服务器启动，监听 0.0.0.0:2883 地址...
2025-09-20 13:21:19.573[36m [info] [config_checker.go:42] [0m共配置了 2 个MCP服务器:
2025-09-20 13:21:19.573[36m [info] [config_checker.go:82] [0m  [1] ❌ filesystem (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:21:19.573[36m [info] [config_checker.go:82] [0m  [2] ❌ memory (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:21:19.573[36m [info] [config_checker.go:87] [0m配置检查完成: 2个服务器已启用, 2个存在问题
2025-09-20 13:21:19.573[33m [warning] [config_checker.go:90] [0m⚠️  发现配置问题，请检查上述错误并修复
2025-09-20 13:21:19.573[36m [info] [service.go:77] [0mInitial health check passed
2025-09-20 13:21:19.573[37m [debug] [service.go:383] [0mHealth checker started
2025-09-20 13:21:19.573[37m [debug] [service.go:413] [0mChat history flusher started
2025-09-20 13:21:19.573[36m [info] [config_checker.go:101] [0m--- 设备MCP配置检查 ---
2025-09-20 13:21:19.573[36m [info] [config_checker.go:104] [0m设备MCP启用状态: true
2025-09-20 13:21:19.573[36m [info] [config_checker.go:114] [0mWebSocket路径: /xiaozhi/mcp/
2025-09-20 13:21:19.573[36m [info] [config_checker.go:115] [0m每设备最大连接数: 5
2025-09-20 13:21:19.573[36m [info] [config_checker.go:96] [0m=== MCP配置检查完成 ===
2025-09-20 13:21:19.573[36m [info] [global_manage.go:103] [0m从配置中读取到 2 个MCP服务器配置
2025-09-20 13:21:19.573[36m [info] [global_manage.go:107] [0mMCP服务器[1]: Type=sse, Name=filesystem, Url=http://localhost:3001/sse, SSEUrl=, Enabled=true
2025-09-20 13:21:19.573[36m [info] [global_manage.go:107] [0mMCP服务器[2]: Type=streamablehttp, Name=memory, Url=http://localhost:3002/mcp, SSEUrl=, Enabled=true
2025-09-20 13:21:19.573[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: filesystem (URL: )
2025-09-20 13:21:19.573[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: filesystem, SSE URL: 
2025-09-20 13:21:19.573[36m [info] [service.go:85] [0mManager-API service started successfully
2025-09-20 13:21:19.573[36m [info] [app.go:67] [0mmanager-api service started successfully
2025-09-20 13:21:19.573[31m [error] [global_manage.go:233] [0m启动MCP客户端失败，服务器: filesystem, 错误: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:21:19.573[31m [error] [global_manage.go:116] [0m连接到MCP服务器 filesystem 失败: 连接MCP服务器失败: 启动客户端失败: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:21:19.573[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: memory (URL: )
2025-09-20 13:21:19.573[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: memory, SSE URL: 
2025-09-20 13:21:19.574[36m [info] [global_manage.go:237] [0mMCP客户端启动成功: memory
2025-09-20 13:21:19.574[36m [info] [global_manage.go:253] [0m正在初始化MCP服务器: memory
2025-09-20 13:21:19.574[31m [error] [global_manage.go:256] [0m初始化MCP服务器失败，服务器: memory, 错误: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:21:19.574[31m [error] [global_manage.go:116] [0m连接到MCP服务器 memory 失败: 连接MCP服务器失败: 初始化失败: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:21:19.574[36m [info] [global_manage.go:126] [0m成功连接了 0 个MCP服务器
2025-09-20 13:21:19.574[36m [info] [global_manage.go:131] [0m全局MCP管理器已启动
2025-09-20 13:21:19.574[36m [info] [manager.go:64] [0m设备MCP管理器将根据连接动态创建
2025-09-20 13:21:19.574[36m [info] [manager.go:67] [0m=== MCP管理器集群启动完成 ===
2025-09-20 13:21:19.574[36m [info] [manager.go:130] [0mMCP管理器启动统计:
2025-09-20 13:21:19.574[36m [info] [manager.go:131] [0m  - 本地工具数量: 0
2025-09-20 13:21:19.574[36m [info] [manager.go:132] [0m  - 全局工具数量: 0
2025-09-20 13:21:19.574[36m [info] [manager.go:133] [0m  - 设备管理器: 动态管理
2025-09-20 13:21:19.574[36m [info] [manager.go:134] [0m  - 总计工具数量: 0
2025-09-20 13:21:19.574[36m [info] [websocket_server.go:103] [0mWebSocket 服务器启动在 ws://0.0.0.0:8989/xiaozhi/v1/
2025-09-20 13:21:19.574[36m [info] [websocket_server.go:104] [0mMCP WebSocket 端点: ws://0.0.0.0:8989/xiaozhi/mcp/{deviceId}
2025-09-20 13:21:19.574[36m [info] [websocket_server.go:105] [0mMCP API 端点: http://0.0.0.0:8989/xiaozhi/api/mcp/tools/{deviceId}
2025-09-20 13:21:20.573[36m [info] [local_mcp_tool.go:28] [0m初始化聊天相关的本地MCP工具...
2025-09-20 13:21:20.573[36m [info] [mqtt_udp_adapter.go:74] [0mMqttUdpAdapter开始启动，尝试连接MQTT服务器...
2025-09-20 13:21:20.573[36m [info] [mqtt_udp_adapter.go:75] [0mMQTT配置: Broker=%s:%d, ClientID=%s, Username=%s127.0.0.12883xiaozhi_serveradmin
2025-09-20 13:21:20.574[36m [info] [mqtt_udp_adapter.go:88] [0mMQTT已连接
2025-09-20 13:21:20.574[36m [info] [device_hook.go:106] [0m=== 收到订阅包 ===
2025-09-20 13:21:20.574[36m [info] [device_hook.go:107] [0m客户端ID: xiaozhi_server
2025-09-20 13:21:20.574[36m [info] [device_hook.go:108] [0m包类型: 8
2025-09-20 13:21:20.574[36m [info] [device_hook.go:109] [0m包ID: 1
2025-09-20 13:21:20.574[36m [info] [device_hook.go:112] [0m订阅信息:
2025-09-20 13:21:20.574[36m [info] [device_hook.go:114] [0m  1. 主题: /p2p/device_public/#, QoS: 0
2025-09-20 13:21:20.574[36m [info] [device_hook.go:118] [0m==================
2025-09-20 13:21:20.575[36m [info] [local_manager.go:69] [0m成功注册本地工具: exit_conversation - 仅当用户明确表达想要结束对话的意图时使用，例如说"再见"、"退出"、"结束对话"等告别词汇。不要因为用户提问身份、询问功能或进行正常对话而调用此工具
2025-09-20 13:21:20.575[36m [info] [local_manager.go:69] [0m成功注册本地工具: clear_conversation_history - 当用户要求清空、清除或重置历史对话记录时使用，用于清空当前会话的所有历史对话内容
2025-09-20 13:21:20.576[36m [info] [local_manager.go:69] [0m成功注册本地工具: play_music - 播放播客，音乐，电台等音频内容，涉及播放的都可以调用，可以传入内容作者或者名称，如果是随机播放或者播放下一首，传入*即可。参数格式: {\"name\": \"名称\"}
2025-09-20 13:21:20.576[36m [info] [local_mcp_tool.go:73] [0m聊天相关的本地MCP工具初始化完成
2025-09-20 13:21:20.576[36m [info] [app.go:181] [0m聊天相关的本地MCP工具注册完成
2025-09-20 13:21:31.429[36m [info] [websocket_server.go:158] [0m收到连接请求，设备ID: 42:32:13:b2:7b:f2, ClientID:web_test_client
2025-09-20 13:21:31.430[36m [info] [unknown] [base.go:36] [0mRedis用户配置提供者初始化成功
2025-09-20 13:21:31.436[37m [debug] [service.go:193] [0mGetDeviceConfig config: &{DeviceMaxOutputSize:0 ChatHistoryConf:2 Plugins:map[] McpEndpoint: Voiceprint:<nil> SelectedModule:map[ASR:ASR_DoubaoStreamASR Intent:Intent_function_call LLM:LLM_AliLLM Memory:Memory_mem0ai TTS:TTS_DoubaoTTS VAD:VAD_SileroVAD VLLM:VLLM_ChatGLMVLLM] Prompt:[角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话 Memory:map[Memory_mem0ai:map[api_key:你的api_key type:mem0ai]] VAD:map[VAD_SileroVAD:map[min_silence_duration_ms:700 model_dir:models/snakers4_silero-vad threshold:0.5 type:silero]] ASR:map[ASR_DoubaoStreamASR:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]] LLM:map[LLM_AliLLM:map[api_key:sk-e17e481f8e90416e861d7f095cb7534d base_url:https://dashscope.aliyuncs.com/compatible-mode/v1 frequency_penalty:0 max_tokens:500 model_name:qwen2.5-72b-instruct temperature:0.8 top_k:50 top_p:1 type:openai]] VLLM:map[VLLM_ChatGLMVLLM:map[api_key:你的api_key base_url:https://open.bigmodel.cn/api/paas/v4/ model_name:glm-4v-flash type:openai]] TTS:map[TTS_DoubaoTTS:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:]] Intent:map[Intent_function_call:map[type:function_call]]}
2025-09-20 13:21:31.436[37m [debug] [service.go:201] [0mDevice configuration retrieved and cached for 42:32:13:b2:7b:f2
2025-09-20 13:21:31.436[36m [info] [chat.go:228] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置: {"device_max_output_size":"0","chat_history_conf":2,"selected_module":{"ASR":"ASR_DoubaoStreamASR","Intent":"Intent_function_call","LLM":"LLM_AliLLM","Memory":"Memory_mem0ai","TTS":"TTS_DoubaoTTS","VAD":"VAD_SileroVAD","VLLM":"VLLM_ChatGLMVLLM"},"prompt":"[角色设定]\n你是小云，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话","Memory":{"Memory_mem0ai":{"api_key":"你的api_key","type":"mem0ai"}},"VAD":{"VAD_SileroVAD":{"min_silence_duration_ms":700,"model_dir":"models/snakers4_silero-vad","threshold":0.5,"type":"silero"}},"ASR":{"ASR_DoubaoStreamASR":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","appid":"**********","boosting_table_name":"","cluster":"volcengine_input_common","correct_table_name":"","output_dir":"tmp/","type":"doubao_stream"}},"LLM":{"LLM_AliLLM":{"api_key":"sk-e17e481f8e90416e861d7f095cb7534d","base_url":"https://dashscope.aliyuncs.com/compatible-mode/v1","frequency_penalty":"0","max_tokens":"500","model_name":"qwen2.5-72b-instruct","temperature":"0.8","top_k":"50","top_p":"1","type":"openai"}},"VLLM":{"VLLM_ChatGLMVLLM":{"api_key":"你的api_key","base_url":"https://open.bigmodel.cn/api/paas/v4/","model_name":"glm-4v-flash","type":"openai"}},"TTS":{"TTS_DoubaoTTS":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","api_url":"https://openspeech.bytedance.com/api/v1/tts","appid":"**********","authorization":"Bearer;","cluster":"volcano_tts","output_dir":"tmp/","pitch_ratio":"","private_voice":"BV051_streaming","ref_audio":"https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3","speed_ratio":"1.5","type":"doubao","voice":"BV001_streaming","volume_ratio":""}},"Intent":{"Intent_function_call":{"type":"function_call"}}}
2025-09-20 13:21:31.436[36m [info] [chat.go:278] [0m设备 42:32:13:b2:7b:f2 配置转换完成: VAD=webrtc_vad, ASR=doubao, LLM=LLM_AliLLM, TTS=doubao_ws TTS-CONFIG:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:] Memory=long_term
2025-09-20 13:21:31.436[36m [info] [chat.go:234] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置
2025-09-20 13:21:31.436[36m [info] [unknown] [factory.go:41] [0m短记忆创建成功: short_term_redis
2025-09-20 13:21:31.436[36m [info] [unknown] [long_term.go:34] [0m正在创建长记忆提供者: memobase
2025-09-20 13:21:31.440[36m [info] [unknown] [memobase_provider.go:79] [0mMemobase客户端初始化完成: http://xiaozhi.localhost:8777
2025-09-20 13:21:31.440[36m [info] [unknown] [factory.go:35] [0mMemobase提供者初始化成功: http://xiaozhi.localhost:8777
2025-09-20 13:21:31.440[36m [info] [unknown] [long_term.go:40] [0m长记忆管理器已启动，使用提供者: memobase
2025-09-20 13:21:31.441[36m [info] [unknown] [factory.go:46] [0m长记忆创建成功: long_term_memobase
2025-09-20 13:21:31.441[33m [warning] [unknown] [memory.go:144] [0m长记忆不支持直接获取历史消息
2025-09-20 13:21:31.441[36m [info] [chat.go:133] [0m为设备 42:32:13:b2:7b:f2 从全局记忆加载了 0 条历史消息
2025-09-20 13:21:31.441[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:21:31.443[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:21:31.444[36m [info] [chat.go:141] [0m为设备 42:32:13:b2:7b:f2 在会话初始化时获取到用户画像（全局配置），长度: 138 字符
2025-09-20 13:21:31.445[36m [info] [client.go:406] [0msaveMemory response body: {"code":401,"msg":"认证失败: 无效的令牌"}
2025-09-20 13:21:31.445[31m [error] [client.go:587] [0mNon-retryable error for /agent/saveMemory/42:32:13:b2:7b:f2 on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:21:31.445[31m [error] [service.go:249] [0mFailed to save memory for device 42:32:13:b2:7b:f2: authentication error: Invalid or missing authentication token
2025-09-20 13:21:31.445[33m [warning] [chat.go:144] [0m保存设备 42:32:13:b2:7b:f2 的用户画像失败: authentication error: Invalid or missing authentication token
2025-09-20 13:21:31.445[36m [info] [device_status_manager.go:55] [0m设备 42:32:13:b2:7b:f2 开始新会话，固件版本: unknown
2025-09-20 13:21:31.445[36m [info] [chat.go:165] [0m设备 42:32:13:b2:7b:f2 开始状态跟踪，固件版本: unknown
2025-09-20 13:21:31.445[37m [debug] [eino_llm.go:165] [0mopenaiConfig: &{APIKey:sk-e17e481f8e90416e861d7f095cb7534d Timeout:0s HTTPClient:<nil> ByAzure:false AzureModelMapperFunc:<nil> BaseURL:https://dashscope.aliyuncs.com/compatible-mode/v1 APIVersion: Model:qwen2.5-72b-instruct MaxTokens:0xc0004563e0 Temperature:<nil> TopP:<nil> Stop:[] PresencePenalty:<nil> ResponseFormat:<nil> Seed:<nil> FrequencyPenalty:<nil> LogitBias:map[] User:<nil> ExtraFields:map[]}
2025-09-20 13:21:31.445[36m [info] [eino_llm.go:173] [0m成功创建OpenAI ChatModel，模型: qwen2.5-72b-instruct
2025-09-20 13:21:31.445[36m [info] [client.go:325] [0m初始化asr, asrConfig: {Provider:doubao Config:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]}
2025-09-20 13:21:31.445[37m [debug] [session.go:593] [0mprocessChatText start
2025-09-20 13:21:31.445[36m [info] [session.go:137] [0m收到文本消息: {"type":"hello","device_id":"42:32:13:b2:7b:f2","device_name":"Web测试设备","device_mac":"42:32:13:b2:7b:f2","token":"your-token1","features":{"mcp":true}}
2025-09-20 13:21:39.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:21:39.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:21:39.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:21:46.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:21:49.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:21:49.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:21:49.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:21:59.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:21:59.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:21:59.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:09.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:09.573[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:09.573[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:19.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:19.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:19.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:29.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:29.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:29.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:31.446[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:22:39.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:39.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:39.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:46.448[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:22:49.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:49.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:49.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:22:59.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:22:59.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:22:59.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:09.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:09.573[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:09.573[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:19.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:19.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:19.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:29.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:29.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:29.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:31.446[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:23:39.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:39.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:39.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:46.448[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:23:49.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:49.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:49.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:23:59.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:23:59.573[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:23:59.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:09.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:09.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:09.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:19.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:19.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:19.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:29.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:29.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:29.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:31.446[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:24:39.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:39.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:39.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:46.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:24:49.572[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:49.573[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:49.573[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:24:59.573[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:24:59.574[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:24:59.574[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: authentication error: Invalid or missing authentication token
2025-09-20 13:28:20.925[36m [info] [unknown] [once.go:78] [0mRedis客户端初始化成功
2025-09-20 13:28:20.925[36m [info] [main.go:100] [0mpprof服务已禁用
2025-09-20 13:28:20.926[36m [info] [udp_server.go:61] [0mUDP服务器启动在 0.0.0.0:8990
2025-09-20 13:28:20.926[36m [info] [app.go:239] [0mmanager-api service initialized successfully
2025-09-20 13:28:20.926[36m [info] [app.go:51] [0m设备状态管理器初始化成功
2025-09-20 13:28:20.926[36m [info] [service.go:71] [0mStarting Manager-API service...
2025-09-20 13:28:20.927[36m [info] [main.go:116] [0m服务器已启动，按 Ctrl+C 退出
2025-09-20 13:28:20.928[36m [info] [local_manager.go:43] [0m本地MCP管理器默认工具初始化完成
2025-09-20 13:28:20.928[36m [info] [manager.go:47] [0m=== 启动MCP管理器集群 ===
2025-09-20 13:28:20.928[36m [info] [manager.go:50] [0m启动本地MCP管理器...
2025-09-20 13:28:20.928[36m [info] [local_manager.go:172] [0m本地MCP管理器已启动
2025-09-20 13:28:20.928[36m [info] [manager.go:57] [0m启动全局MCP管理器...
2025-09-20 13:28:20.928[36m [info] [config_checker.go:14] [0m=== MCP配置检查 ===
2025-09-20 13:28:20.928[36m [info] [config_checker.go:18] [0m全局MCP启用状态: true
2025-09-20 13:28:20.928[36m [info] [config_checker.go:28] [0m重连配置: 间隔=300秒, 最大尝试次数=10
2025-09-20 13:28:20.929[36m [info] [config_checker.go:42] [0m共配置了 2 个MCP服务器:
2025-09-20 13:28:20.929[36m [info] [config_checker.go:82] [0m  [1] ❌ filesystem (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:28:20.929[36m [info] [config_checker.go:82] [0m  [2] ❌ memory (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:28:20.929[36m [info] [config_checker.go:87] [0m配置检查完成: 2个服务器已启用, 2个存在问题
2025-09-20 13:28:20.929[33m [warning] [config_checker.go:90] [0m⚠️  发现配置问题，请检查上述错误并修复
2025-09-20 13:28:20.929[36m [info] [config_checker.go:101] [0m--- 设备MCP配置检查 ---
2025-09-20 13:28:20.929[36m [info] [config_checker.go:104] [0m设备MCP启用状态: true
2025-09-20 13:28:20.929[36m [info] [config_checker.go:114] [0mWebSocket路径: /xiaozhi/mcp/
2025-09-20 13:28:20.929[36m [info] [config_checker.go:115] [0m每设备最大连接数: 5
2025-09-20 13:28:20.929[36m [info] [config_checker.go:96] [0m=== MCP配置检查完成 ===
2025-09-20 13:28:20.929[36m [info] [mqtt_server.go:80] [0mMQTT 服务器启动，监听 0.0.0.0:2883 地址...
2025-09-20 13:28:20.929[36m [info] [global_manage.go:103] [0m从配置中读取到 2 个MCP服务器配置
2025-09-20 13:28:20.929[36m [info] [global_manage.go:107] [0mMCP服务器[1]: Type=sse, Name=filesystem, Url=http://localhost:3001/sse, SSEUrl=, Enabled=true
2025-09-20 13:28:20.929[36m [info] [global_manage.go:107] [0mMCP服务器[2]: Type=streamablehttp, Name=memory, Url=http://localhost:3002/mcp, SSEUrl=, Enabled=true
2025-09-20 13:28:20.929[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: filesystem (URL: )
2025-09-20 13:28:20.929[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: filesystem, SSE URL: 
2025-09-20 13:28:20.930[36m [info] [service.go:77] [0mInitial health check passed
2025-09-20 13:28:20.930[37m [debug] [service.go:383] [0mHealth checker started
2025-09-20 13:28:20.930[37m [debug] [service.go:413] [0mChat history flusher started
2025-09-20 13:28:20.930[36m [info] [service.go:85] [0mManager-API service started successfully
2025-09-20 13:28:20.930[36m [info] [app.go:67] [0mmanager-api service started successfully
2025-09-20 13:28:20.930[31m [error] [global_manage.go:233] [0m启动MCP客户端失败，服务器: filesystem, 错误: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:28:20.930[31m [error] [global_manage.go:116] [0m连接到MCP服务器 filesystem 失败: 连接MCP服务器失败: 启动客户端失败: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:28:20.930[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: memory (URL: )
2025-09-20 13:28:20.930[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: memory, SSE URL: 
2025-09-20 13:28:20.930[36m [info] [global_manage.go:237] [0mMCP客户端启动成功: memory
2025-09-20 13:28:20.930[36m [info] [global_manage.go:253] [0m正在初始化MCP服务器: memory
2025-09-20 13:28:20.931[31m [error] [global_manage.go:256] [0m初始化MCP服务器失败，服务器: memory, 错误: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:28:20.931[31m [error] [global_manage.go:116] [0m连接到MCP服务器 memory 失败: 连接MCP服务器失败: 初始化失败: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:28:20.931[36m [info] [global_manage.go:126] [0m成功连接了 0 个MCP服务器
2025-09-20 13:28:20.931[36m [info] [global_manage.go:131] [0m全局MCP管理器已启动
2025-09-20 13:28:20.931[36m [info] [manager.go:64] [0m设备MCP管理器将根据连接动态创建
2025-09-20 13:28:20.931[36m [info] [manager.go:67] [0m=== MCP管理器集群启动完成 ===
2025-09-20 13:28:20.931[36m [info] [manager.go:130] [0mMCP管理器启动统计:
2025-09-20 13:28:20.931[36m [info] [manager.go:131] [0m  - 本地工具数量: 0
2025-09-20 13:28:20.931[36m [info] [manager.go:132] [0m  - 全局工具数量: 0
2025-09-20 13:28:20.931[36m [info] [manager.go:133] [0m  - 设备管理器: 动态管理
2025-09-20 13:28:20.931[36m [info] [manager.go:134] [0m  - 总计工具数量: 0
2025-09-20 13:28:20.931[36m [info] [websocket_server.go:103] [0mWebSocket 服务器启动在 ws://0.0.0.0:8989/xiaozhi/v1/
2025-09-20 13:28:20.931[36m [info] [websocket_server.go:104] [0mMCP WebSocket 端点: ws://0.0.0.0:8989/xiaozhi/mcp/{deviceId}
2025-09-20 13:28:20.931[36m [info] [websocket_server.go:105] [0mMCP API 端点: http://0.0.0.0:8989/xiaozhi/api/mcp/tools/{deviceId}
2025-09-20 13:28:21.927[36m [info] [local_mcp_tool.go:28] [0m初始化聊天相关的本地MCP工具...
2025-09-20 13:28:21.927[36m [info] [mqtt_udp_adapter.go:74] [0mMqttUdpAdapter开始启动，尝试连接MQTT服务器...
2025-09-20 13:28:21.927[36m [info] [mqtt_udp_adapter.go:75] [0mMQTT配置: Broker=%s:%d, ClientID=%s, Username=%s127.0.0.12883xiaozhi_serveradmin
2025-09-20 13:28:21.928[36m [info] [mqtt_udp_adapter.go:88] [0mMQTT已连接
2025-09-20 13:28:21.928[36m [info] [device_hook.go:106] [0m=== 收到订阅包 ===
2025-09-20 13:28:21.928[36m [info] [device_hook.go:107] [0m客户端ID: xiaozhi_server
2025-09-20 13:28:21.928[36m [info] [device_hook.go:108] [0m包类型: 8
2025-09-20 13:28:21.928[36m [info] [device_hook.go:109] [0m包ID: 1
2025-09-20 13:28:21.928[36m [info] [device_hook.go:112] [0m订阅信息:
2025-09-20 13:28:21.928[36m [info] [device_hook.go:114] [0m  1. 主题: /p2p/device_public/#, QoS: 0
2025-09-20 13:28:21.928[36m [info] [device_hook.go:118] [0m==================
2025-09-20 13:28:21.929[36m [info] [local_manager.go:69] [0m成功注册本地工具: exit_conversation - 仅当用户明确表达想要结束对话的意图时使用，例如说"再见"、"退出"、"结束对话"等告别词汇。不要因为用户提问身份、询问功能或进行正常对话而调用此工具
2025-09-20 13:28:21.929[36m [info] [local_manager.go:69] [0m成功注册本地工具: clear_conversation_history - 当用户要求清空、清除或重置历史对话记录时使用，用于清空当前会话的所有历史对话内容
2025-09-20 13:28:21.930[36m [info] [local_manager.go:69] [0m成功注册本地工具: play_music - 播放播客，音乐，电台等音频内容，涉及播放的都可以调用，可以传入内容作者或者名称，如果是随机播放或者播放下一首，传入*即可。参数格式: {\"name\": \"名称\"}
2025-09-20 13:28:21.930[36m [info] [local_mcp_tool.go:73] [0m聊天相关的本地MCP工具初始化完成
2025-09-20 13:28:21.930[36m [info] [app.go:181] [0m聊天相关的本地MCP工具注册完成
2025-09-20 13:29:01.281[36m [info] [websocket_server.go:158] [0m收到连接请求，设备ID: 42:32:13:b2:7b:f2, ClientID:web_test_client
2025-09-20 13:29:01.282[36m [info] [unknown] [base.go:36] [0mRedis用户配置提供者初始化成功
2025-09-20 13:29:01.293[37m [debug] [service.go:193] [0mGetDeviceConfig config: &{DeviceMaxOutputSize:0 ChatHistoryConf:2 Plugins:map[] McpEndpoint: Voiceprint:<nil> SelectedModule:map[ASR:ASR_DoubaoStreamASR Intent:Intent_function_call LLM:LLM_AliLLM Memory:Memory_mem0ai TTS:TTS_DoubaoTTS VAD:VAD_SileroVAD VLLM:VLLM_ChatGLMVLLM] Prompt:[角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话 Memory:map[Memory_mem0ai:map[api_key:你的api_key type:mem0ai]] VAD:map[VAD_SileroVAD:map[min_silence_duration_ms:700 model_dir:models/snakers4_silero-vad threshold:0.5 type:silero]] ASR:map[ASR_DoubaoStreamASR:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]] LLM:map[LLM_AliLLM:map[api_key:sk-e17e481f8e90416e861d7f095cb7534d base_url:https://dashscope.aliyuncs.com/compatible-mode/v1 frequency_penalty:0 max_tokens:500 model_name:qwen2.5-72b-instruct temperature:0.8 top_k:50 top_p:1 type:openai]] VLLM:map[VLLM_ChatGLMVLLM:map[api_key:你的api_key base_url:https://open.bigmodel.cn/api/paas/v4/ model_name:glm-4v-flash type:openai]] TTS:map[TTS_DoubaoTTS:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:]] Intent:map[Intent_function_call:map[type:function_call]]}
2025-09-20 13:29:01.293[37m [debug] [service.go:201] [0mDevice configuration retrieved and cached for 42:32:13:b2:7b:f2
2025-09-20 13:29:01.293[36m [info] [chat.go:228] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置: {"device_max_output_size":"0","chat_history_conf":2,"selected_module":{"ASR":"ASR_DoubaoStreamASR","Intent":"Intent_function_call","LLM":"LLM_AliLLM","Memory":"Memory_mem0ai","TTS":"TTS_DoubaoTTS","VAD":"VAD_SileroVAD","VLLM":"VLLM_ChatGLMVLLM"},"prompt":"[角色设定]\n你是小云，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话","Memory":{"Memory_mem0ai":{"api_key":"你的api_key","type":"mem0ai"}},"VAD":{"VAD_SileroVAD":{"min_silence_duration_ms":700,"model_dir":"models/snakers4_silero-vad","threshold":0.5,"type":"silero"}},"ASR":{"ASR_DoubaoStreamASR":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","appid":"**********","boosting_table_name":"","cluster":"volcengine_input_common","correct_table_name":"","output_dir":"tmp/","type":"doubao_stream"}},"LLM":{"LLM_AliLLM":{"api_key":"sk-e17e481f8e90416e861d7f095cb7534d","base_url":"https://dashscope.aliyuncs.com/compatible-mode/v1","frequency_penalty":"0","max_tokens":"500","model_name":"qwen2.5-72b-instruct","temperature":"0.8","top_k":"50","top_p":"1","type":"openai"}},"VLLM":{"VLLM_ChatGLMVLLM":{"api_key":"你的api_key","base_url":"https://open.bigmodel.cn/api/paas/v4/","model_name":"glm-4v-flash","type":"openai"}},"TTS":{"TTS_DoubaoTTS":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","api_url":"https://openspeech.bytedance.com/api/v1/tts","appid":"**********","authorization":"Bearer;","cluster":"volcano_tts","output_dir":"tmp/","pitch_ratio":"","private_voice":"BV051_streaming","ref_audio":"https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3","speed_ratio":"1.5","type":"doubao","voice":"BV001_streaming","volume_ratio":""}},"Intent":{"Intent_function_call":{"type":"function_call"}}}
2025-09-20 13:29:01.293[36m [info] [chat.go:278] [0m设备 42:32:13:b2:7b:f2 配置转换完成: VAD=webrtc_vad, ASR=doubao, LLM=LLM_AliLLM, TTS=doubao_ws TTS-CONFIG:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:] Memory=long_term
2025-09-20 13:29:01.293[36m [info] [chat.go:234] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置
2025-09-20 13:29:01.293[36m [info] [unknown] [factory.go:41] [0m短记忆创建成功: short_term_redis
2025-09-20 13:29:01.293[36m [info] [unknown] [long_term.go:34] [0m正在创建长记忆提供者: memobase
2025-09-20 13:29:01.296[36m [info] [unknown] [memobase_provider.go:79] [0mMemobase客户端初始化完成: http://xiaozhi.localhost:8777
2025-09-20 13:29:01.296[36m [info] [unknown] [factory.go:35] [0mMemobase提供者初始化成功: http://xiaozhi.localhost:8777
2025-09-20 13:29:01.297[36m [info] [unknown] [long_term.go:40] [0m长记忆管理器已启动，使用提供者: memobase
2025-09-20 13:29:01.297[36m [info] [unknown] [factory.go:46] [0m长记忆创建成功: long_term_memobase
2025-09-20 13:29:01.297[33m [warning] [unknown] [memory.go:144] [0m长记忆不支持直接获取历史消息
2025-09-20 13:29:01.297[36m [info] [chat.go:133] [0m为设备 42:32:13:b2:7b:f2 从全局记忆加载了 0 条历史消息
2025-09-20 13:29:01.297[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:29:01.300[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:29:01.302[36m [info] [chat.go:141] [0m为设备 42:32:13:b2:7b:f2 在会话初始化时获取到用户画像（全局配置），长度: 138 字符
2025-09-20 13:29:01.303[36m [info] [client.go:406] [0msaveMemory response body: {"code":401,"msg":"认证失败: 无效的令牌"}
2025-09-20 13:29:01.303[31m [error] [client.go:587] [0mNon-retryable error for /agent/saveMemory/42:32:13:b2:7b:f2 on http://127.0.0.1:8003/xiaozhi: authentication error: Invalid or missing authentication token
2025-09-20 13:29:01.303[31m [error] [service.go:249] [0mFailed to save memory for device 42:32:13:b2:7b:f2: authentication error: Invalid or missing authentication token
2025-09-20 13:29:01.303[33m [warning] [chat.go:144] [0m保存设备 42:32:13:b2:7b:f2 的用户画像失败: authentication error: Invalid or missing authentication token
2025-09-20 13:29:01.303[36m [info] [device_status_manager.go:55] [0m设备 42:32:13:b2:7b:f2 开始新会话，固件版本: unknown
2025-09-20 13:29:01.303[36m [info] [chat.go:165] [0m设备 42:32:13:b2:7b:f2 开始状态跟踪，固件版本: unknown
2025-09-20 13:29:01.304[37m [debug] [eino_llm.go:165] [0mopenaiConfig: &{APIKey:sk-e17e481f8e90416e861d7f095cb7534d Timeout:0s HTTPClient:<nil> ByAzure:false AzureModelMapperFunc:<nil> BaseURL:https://dashscope.aliyuncs.com/compatible-mode/v1 APIVersion: Model:qwen2.5-72b-instruct MaxTokens:0xc0003fa3e0 Temperature:<nil> TopP:<nil> Stop:[] PresencePenalty:<nil> ResponseFormat:<nil> Seed:<nil> FrequencyPenalty:<nil> LogitBias:map[] User:<nil> ExtraFields:map[]}
2025-09-20 13:29:01.304[36m [info] [eino_llm.go:173] [0m成功创建OpenAI ChatModel，模型: qwen2.5-72b-instruct
2025-09-20 13:29:01.304[36m [info] [client.go:325] [0m初始化asr, asrConfig: {Provider:doubao Config:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]}
2025-09-20 13:29:01.304[37m [debug] [session.go:593] [0mprocessChatText start
2025-09-20 13:29:01.304[36m [info] [session.go:137] [0m收到文本消息: {"type":"hello","device_id":"42:32:13:b2:7b:f2","device_name":"Web测试设备","device_mac":"42:32:13:b2:7b:f2","token":"your-token1","features":{"mcp":true}}
2025-09-20 13:29:10.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:29:10.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:29:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:29:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:29:20.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:29:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:29:30.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:29:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:29:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:29:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:29:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:00.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:01.304[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:30:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:10.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:30:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:30.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:40.942[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:30:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:30:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:00.930[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:01.304[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:31:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:10.953[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:31:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:30.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:40.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:31:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:31:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:32:10.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:10.953[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:32:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:20.930[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:30.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:40.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:40.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:32:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:32:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:00.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:33:10.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:10.954[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:33:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:20.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:30.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:40.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:33:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:33:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:34:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:10.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:34:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:30.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:30.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:40.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:40.966[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:34:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:34:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:00.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:35:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:10.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:35:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:30.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:40.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:35:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:35:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:36:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:10.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:36:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:40.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:36:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:36:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:00.968[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:01.304[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:37:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:10.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:37:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:40.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:37:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:37:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:01.304[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:38:10.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:10.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:38:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:40.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:38:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:38:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:00.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:39:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:10.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:39:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:39:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:39:50.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:00.930[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:40:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:10.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:40:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:40:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:40:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:00.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:41:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:10.935[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:41:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:20.937[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:30.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:40.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:41:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:41:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:42:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:10.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:42:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:20.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:30.935[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:40.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:42:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:42:50.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:43:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:00.929[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: API request failed: request failed: Post "http://127.0.0.1:8003/xiaozhi/device/report-status": dial tcp 127.0.0.1:8003: connect: connection refused
2025-09-20 13:43:00.929[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: API request failed: request failed: Post "http://127.0.0.1:8003/xiaozhi/device/report-status": dial tcp 127.0.0.1:8003: connect: connection refused
2025-09-20 13:43:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:43:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:10.935[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 19秒
2025-09-20 13:43:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:43:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:20.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:43:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:30.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:43:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:43:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:43:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:00.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:44:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:10.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:44:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:30.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:40.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:40.935[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:44:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:44:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:00.965[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:01.304[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:45:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:10.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:45:20.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:20.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:40.969[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:45:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:45:50.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:00.966[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:46:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:10.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:46:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:40.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:46:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:46:50.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:00.968[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:47:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:10.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:47:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:20.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:30.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:40.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:47:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:47:50.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:00.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:00.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:48:10.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:10.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:16.306[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:48:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:20.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:30.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:40.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:48:50.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:48:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:00.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:00.931[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:01.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:49:10.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:10.930[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:16.305[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:49:20.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:20.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:30.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:30.932[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:40.926[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:40.934[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:50.927[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:49:50.933[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:49:59.461[36m [info] [main.go:119] [0m正在关闭服务器...
2025-09-20 13:49:59.461[36m [info] [app.go:250] [0m开始关闭应用服务...
2025-09-20 13:49:59.461[36m [info] [app.go:254] [0m关闭manager-api服务...
2025-09-20 13:49:59.461[36m [info] [service.go:98] [0mStopping Manager-API service...
2025-09-20 13:49:59.462[36m [info] [service.go:125] [0mAll workers stopped gracefully
2025-09-20 13:49:59.462[36m [info] [service.go:131] [0mManager-API service stopped
2025-09-20 13:49:59.462[36m [info] [app.go:258] [0mmanager-api服务已关闭
2025-09-20 13:49:59.462[36m [info] [app.go:271] [0m执行全局资源清理...
2025-09-20 13:49:59.462[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:49:59.462[36m [info] [app.go:276] [0m全局资源清理完成
2025-09-20 13:49:59.462[36m [info] [app.go:279] [0m应用服务关闭完成
2025-09-20 13:49:59.462[36m [info] [main.go:130] [0m服务器已关闭
2025-09-20 13:49:59.462[36m [info] [main.go:57] [0m程序退出: 执行最终的全局资源清理（备用机制）...
2025-09-20 13:49:59.462[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:49:59.462[36m [info] [main.go:61] [0m程序退出: 最终全局资源清理完成
2025-09-20 13:50:03.852[36m [info] [unknown] [once.go:78] [0mRedis客户端初始化成功
2025-09-20 13:50:03.853[36m [info] [main.go:100] [0mpprof服务已禁用
2025-09-20 13:50:03.853[36m [info] [udp_server.go:61] [0mUDP服务器启动在 0.0.0.0:8990
2025-09-20 13:50:03.853[36m [info] [app.go:239] [0mmanager-api service initialized successfully
2025-09-20 13:50:03.853[36m [info] [app.go:51] [0m设备状态管理器初始化成功
2025-09-20 13:50:03.853[36m [info] [service.go:71] [0mStarting Manager-API service...
2025-09-20 13:50:03.853[36m [info] [main.go:116] [0m服务器已启动，按 Ctrl+C 退出
2025-09-20 13:50:03.853[36m [info] [local_manager.go:43] [0m本地MCP管理器默认工具初始化完成
2025-09-20 13:50:03.853[36m [info] [manager.go:47] [0m=== 启动MCP管理器集群 ===
2025-09-20 13:50:03.853[36m [info] [manager.go:50] [0m启动本地MCP管理器...
2025-09-20 13:50:03.853[36m [info] [local_manager.go:172] [0m本地MCP管理器已启动
2025-09-20 13:50:03.853[36m [info] [manager.go:57] [0m启动全局MCP管理器...
2025-09-20 13:50:03.853[36m [info] [config_checker.go:14] [0m=== MCP配置检查 ===
2025-09-20 13:50:03.853[36m [info] [config_checker.go:18] [0m全局MCP启用状态: true
2025-09-20 13:50:03.853[36m [info] [config_checker.go:28] [0m重连配置: 间隔=300秒, 最大尝试次数=10
2025-09-20 13:50:03.854[36m [info] [config_checker.go:42] [0m共配置了 2 个MCP服务器:
2025-09-20 13:50:03.854[36m [info] [config_checker.go:82] [0m  [1] ❌ filesystem (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:50:03.854[36m [info] [config_checker.go:82] [0m  [2] ❌ memory (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:50:03.854[36m [info] [config_checker.go:87] [0m配置检查完成: 2个服务器已启用, 2个存在问题
2025-09-20 13:50:03.854[33m [warning] [config_checker.go:90] [0m⚠️  发现配置问题，请检查上述错误并修复
2025-09-20 13:50:03.854[36m [info] [config_checker.go:101] [0m--- 设备MCP配置检查 ---
2025-09-20 13:50:03.854[36m [info] [mqtt_server.go:80] [0mMQTT 服务器启动，监听 0.0.0.0:2883 地址...
2025-09-20 13:50:03.854[36m [info] [config_checker.go:104] [0m设备MCP启用状态: true
2025-09-20 13:50:03.854[36m [info] [config_checker.go:114] [0mWebSocket路径: /xiaozhi/mcp/
2025-09-20 13:50:03.854[36m [info] [config_checker.go:115] [0m每设备最大连接数: 5
2025-09-20 13:50:03.854[36m [info] [config_checker.go:96] [0m=== MCP配置检查完成 ===
2025-09-20 13:50:03.854[36m [info] [global_manage.go:103] [0m从配置中读取到 2 个MCP服务器配置
2025-09-20 13:50:03.854[36m [info] [global_manage.go:107] [0mMCP服务器[1]: Type=sse, Name=filesystem, Url=http://localhost:3001/sse, SSEUrl=, Enabled=true
2025-09-20 13:50:03.855[36m [info] [global_manage.go:107] [0mMCP服务器[2]: Type=streamablehttp, Name=memory, Url=http://localhost:3002/mcp, SSEUrl=, Enabled=true
2025-09-20 13:50:03.855[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: filesystem (URL: )
2025-09-20 13:50:03.855[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: filesystem, SSE URL: 
2025-09-20 13:50:03.855[33m [warning] [service.go:75] [0mInitial health check failed: service manager-api unavailable: all URLs are unhealthy
2025-09-20 13:50:03.855[37m [debug] [service.go:383] [0mHealth checker started
2025-09-20 13:50:03.855[37m [debug] [service.go:413] [0mChat history flusher started
2025-09-20 13:50:03.855[36m [info] [service.go:85] [0mManager-API service started successfully
2025-09-20 13:50:03.855[36m [info] [app.go:67] [0mmanager-api service started successfully
2025-09-20 13:50:03.855[31m [error] [global_manage.go:233] [0m启动MCP客户端失败，服务器: filesystem, 错误: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:50:03.855[31m [error] [global_manage.go:116] [0m连接到MCP服务器 filesystem 失败: 连接MCP服务器失败: 启动客户端失败: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:50:03.855[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: memory (URL: )
2025-09-20 13:50:03.855[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: memory, SSE URL: 
2025-09-20 13:50:03.855[36m [info] [global_manage.go:237] [0mMCP客户端启动成功: memory
2025-09-20 13:50:03.855[36m [info] [global_manage.go:253] [0m正在初始化MCP服务器: memory
2025-09-20 13:50:03.855[31m [error] [global_manage.go:256] [0m初始化MCP服务器失败，服务器: memory, 错误: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:50:03.855[31m [error] [global_manage.go:116] [0m连接到MCP服务器 memory 失败: 连接MCP服务器失败: 初始化失败: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:50:03.855[36m [info] [global_manage.go:126] [0m成功连接了 0 个MCP服务器
2025-09-20 13:50:03.855[36m [info] [global_manage.go:131] [0m全局MCP管理器已启动
2025-09-20 13:50:03.855[36m [info] [manager.go:64] [0m设备MCP管理器将根据连接动态创建
2025-09-20 13:50:03.855[36m [info] [manager.go:67] [0m=== MCP管理器集群启动完成 ===
2025-09-20 13:50:03.855[36m [info] [manager.go:130] [0mMCP管理器启动统计:
2025-09-20 13:50:03.855[36m [info] [manager.go:131] [0m  - 本地工具数量: 0
2025-09-20 13:50:03.855[36m [info] [manager.go:132] [0m  - 全局工具数量: 0
2025-09-20 13:50:03.855[36m [info] [manager.go:133] [0m  - 设备管理器: 动态管理
2025-09-20 13:50:03.855[36m [info] [manager.go:134] [0m  - 总计工具数量: 0
2025-09-20 13:50:03.855[36m [info] [websocket_server.go:103] [0mWebSocket 服务器启动在 ws://0.0.0.0:8989/xiaozhi/v1/
2025-09-20 13:50:03.855[36m [info] [websocket_server.go:104] [0mMCP WebSocket 端点: ws://0.0.0.0:8989/xiaozhi/mcp/{deviceId}
2025-09-20 13:50:03.856[36m [info] [websocket_server.go:105] [0mMCP API 端点: http://0.0.0.0:8989/xiaozhi/api/mcp/tools/{deviceId}
2025-09-20 13:50:04.853[36m [info] [local_mcp_tool.go:28] [0m初始化聊天相关的本地MCP工具...
2025-09-20 13:50:04.853[36m [info] [mqtt_udp_adapter.go:74] [0mMqttUdpAdapter开始启动，尝试连接MQTT服务器...
2025-09-20 13:50:04.853[36m [info] [mqtt_udp_adapter.go:75] [0mMQTT配置: Broker=%s:%d, ClientID=%s, Username=%s127.0.0.12883xiaozhi_serveradmin
2025-09-20 13:50:04.856[36m [info] [mqtt_udp_adapter.go:88] [0mMQTT已连接
2025-09-20 13:50:04.856[36m [info] [device_hook.go:106] [0m=== 收到订阅包 ===
2025-09-20 13:50:04.856[36m [info] [device_hook.go:107] [0m客户端ID: xiaozhi_server
2025-09-20 13:50:04.856[36m [info] [device_hook.go:108] [0m包类型: 8
2025-09-20 13:50:04.856[36m [info] [device_hook.go:109] [0m包ID: 1
2025-09-20 13:50:04.856[36m [info] [device_hook.go:112] [0m订阅信息:
2025-09-20 13:50:04.856[36m [info] [device_hook.go:114] [0m  1. 主题: /p2p/device_public/#, QoS: 0
2025-09-20 13:50:04.856[36m [info] [device_hook.go:118] [0m==================
2025-09-20 13:50:04.858[36m [info] [local_manager.go:69] [0m成功注册本地工具: play_music - 播放播客，音乐，电台等音频内容，涉及播放的都可以调用，可以传入内容作者或者名称，如果是随机播放或者播放下一首，传入*即可。参数格式: {\"name\": \"名称\"}
2025-09-20 13:50:04.858[36m [info] [local_manager.go:69] [0m成功注册本地工具: exit_conversation - 仅当用户明确表达想要结束对话的意图时使用，例如说"再见"、"退出"、"结束对话"等告别词汇。不要因为用户提问身份、询问功能或进行正常对话而调用此工具
2025-09-20 13:50:04.858[36m [info] [local_manager.go:69] [0m成功注册本地工具: clear_conversation_history - 当用户要求清空、清除或重置历史对话记录时使用，用于清空当前会话的所有历史对话内容
2025-09-20 13:50:04.858[36m [info] [local_mcp_tool.go:73] [0m聊天相关的本地MCP工具初始化完成
2025-09-20 13:50:04.858[36m [info] [app.go:181] [0m聊天相关的本地MCP工具注册完成
2025-09-20 13:50:33.874[36m [info] [websocket_server.go:158] [0m收到连接请求，设备ID: 42:32:13:b2:7b:f2, ClientID:web_test_client
2025-09-20 13:50:33.874[36m [info] [unknown] [base.go:36] [0mRedis用户配置提供者初始化成功
2025-09-20 13:50:33.882[37m [debug] [service.go:193] [0mGetDeviceConfig config: &{DeviceMaxOutputSize:0 ChatHistoryConf:2 Plugins:map[] McpEndpoint: Voiceprint:<nil> SelectedModule:map[ASR:ASR_DoubaoStreamASR Intent:Intent_function_call LLM:LLM_AliLLM Memory:Memory_mem0ai TTS:TTS_DoubaoTTS VAD:VAD_SileroVAD VLLM:VLLM_ChatGLMVLLM] Prompt:[角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话 Memory:map[Memory_mem0ai:map[api_key:你的api_key type:mem0ai]] VAD:map[VAD_SileroVAD:map[min_silence_duration_ms:700 model_dir:models/snakers4_silero-vad threshold:0.5 type:silero]] ASR:map[ASR_DoubaoStreamASR:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]] LLM:map[LLM_AliLLM:map[api_key:sk-e17e481f8e90416e861d7f095cb7534d base_url:https://dashscope.aliyuncs.com/compatible-mode/v1 frequency_penalty:0 max_tokens:500 model_name:qwen2.5-72b-instruct temperature:0.8 top_k:50 top_p:1 type:openai]] VLLM:map[VLLM_ChatGLMVLLM:map[api_key:你的api_key base_url:https://open.bigmodel.cn/api/paas/v4/ model_name:glm-4v-flash type:openai]] TTS:map[TTS_DoubaoTTS:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:]] Intent:map[Intent_function_call:map[type:function_call]]}
2025-09-20 13:50:33.882[37m [debug] [service.go:201] [0mDevice configuration retrieved and cached for 42:32:13:b2:7b:f2
2025-09-20 13:50:33.882[36m [info] [chat.go:228] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置: {"device_max_output_size":"0","chat_history_conf":2,"selected_module":{"ASR":"ASR_DoubaoStreamASR","Intent":"Intent_function_call","LLM":"LLM_AliLLM","Memory":"Memory_mem0ai","TTS":"TTS_DoubaoTTS","VAD":"VAD_SileroVAD","VLLM":"VLLM_ChatGLMVLLM"},"prompt":"[角色设定]\n你是小云，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话","Memory":{"Memory_mem0ai":{"api_key":"你的api_key","type":"mem0ai"}},"VAD":{"VAD_SileroVAD":{"min_silence_duration_ms":700,"model_dir":"models/snakers4_silero-vad","threshold":0.5,"type":"silero"}},"ASR":{"ASR_DoubaoStreamASR":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","appid":"**********","boosting_table_name":"","cluster":"volcengine_input_common","correct_table_name":"","output_dir":"tmp/","type":"doubao_stream"}},"LLM":{"LLM_AliLLM":{"api_key":"sk-e17e481f8e90416e861d7f095cb7534d","base_url":"https://dashscope.aliyuncs.com/compatible-mode/v1","frequency_penalty":"0","max_tokens":"500","model_name":"qwen2.5-72b-instruct","temperature":"0.8","top_k":"50","top_p":"1","type":"openai"}},"VLLM":{"VLLM_ChatGLMVLLM":{"api_key":"你的api_key","base_url":"https://open.bigmodel.cn/api/paas/v4/","model_name":"glm-4v-flash","type":"openai"}},"TTS":{"TTS_DoubaoTTS":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","api_url":"https://openspeech.bytedance.com/api/v1/tts","appid":"**********","authorization":"Bearer;","cluster":"volcano_tts","output_dir":"tmp/","pitch_ratio":"","private_voice":"BV051_streaming","ref_audio":"https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3","speed_ratio":"1.5","type":"doubao","voice":"BV001_streaming","volume_ratio":""}},"Intent":{"Intent_function_call":{"type":"function_call"}}}
2025-09-20 13:50:33.882[36m [info] [chat.go:278] [0m设备 42:32:13:b2:7b:f2 配置转换完成: VAD=webrtc_vad, ASR=doubao, LLM=LLM_AliLLM, TTS=doubao_ws TTS-CONFIG:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:] Memory=long_term
2025-09-20 13:50:33.882[36m [info] [chat.go:234] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置
2025-09-20 13:50:33.882[36m [info] [unknown] [factory.go:41] [0m短记忆创建成功: short_term_redis
2025-09-20 13:50:33.882[36m [info] [unknown] [long_term.go:34] [0m正在创建长记忆提供者: memobase
2025-09-20 13:50:33.885[36m [info] [unknown] [memobase_provider.go:79] [0mMemobase客户端初始化完成: http://xiaozhi.localhost:8777
2025-09-20 13:50:33.885[36m [info] [unknown] [factory.go:35] [0mMemobase提供者初始化成功: http://xiaozhi.localhost:8777
2025-09-20 13:50:33.885[36m [info] [unknown] [long_term.go:40] [0m长记忆管理器已启动，使用提供者: memobase
2025-09-20 13:50:33.885[36m [info] [unknown] [factory.go:46] [0m长记忆创建成功: long_term_memobase
2025-09-20 13:50:33.885[33m [warning] [unknown] [memory.go:144] [0m长记忆不支持直接获取历史消息
2025-09-20 13:50:33.885[36m [info] [chat.go:133] [0m为设备 42:32:13:b2:7b:f2 从全局记忆加载了 0 条历史消息
2025-09-20 13:50:33.885[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:50:33.887[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:50:33.889[36m [info] [chat.go:141] [0m为设备 42:32:13:b2:7b:f2 在会话初始化时获取到用户画像（全局配置），长度: 138 字符
2025-09-20 13:50:33.894[36m [info] [client.go:406] [0msaveMemory response body: {"code":0,"msg":"success"}
2025-09-20 13:50:33.894[37m [debug] [service.go:262] [0mMemory saved for device 42:32:13:b2:7b:f2
2025-09-20 13:50:33.894[36m [info] [device_status_manager.go:55] [0m设备 42:32:13:b2:7b:f2 开始新会话，固件版本: unknown
2025-09-20 13:50:33.894[36m [info] [chat.go:165] [0m设备 42:32:13:b2:7b:f2 开始状态跟踪，固件版本: unknown
2025-09-20 13:50:33.894[37m [debug] [eino_llm.go:165] [0mopenaiConfig: &{APIKey:sk-e17e481f8e90416e861d7f095cb7534d Timeout:0s HTTPClient:<nil> ByAzure:false AzureModelMapperFunc:<nil> BaseURL:https://dashscope.aliyuncs.com/compatible-mode/v1 APIVersion: Model:qwen2.5-72b-instruct MaxTokens:0xc00030e520 Temperature:<nil> TopP:<nil> Stop:[] PresencePenalty:<nil> ResponseFormat:<nil> Seed:<nil> FrequencyPenalty:<nil> LogitBias:map[] User:<nil> ExtraFields:map[]}
2025-09-20 13:50:33.894[36m [info] [eino_llm.go:173] [0m成功创建OpenAI ChatModel，模型: qwen2.5-72b-instruct
2025-09-20 13:50:33.894[36m [info] [client.go:325] [0m初始化asr, asrConfig: {Provider:doubao Config:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]}
2025-09-20 13:50:33.894[36m [info] [session.go:137] [0m收到文本消息: {"type":"hello","device_id":"42:32:13:b2:7b:f2","device_name":"Web测试设备","device_mac":"42:32:13:b2:7b:f2","token":"your-token1","features":{"mcp":true}}
2025-09-20 13:50:33.894[37m [debug] [session.go:593] [0mprocessChatText start
2025-09-20 13:50:43.853[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:50:43.858[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:50:48.895[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:50:53.853[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:50:53.858[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:03.854[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:03.860[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:13.854[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:13.860[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:23.853[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:23.861[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:33.854[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:33.861[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:33.895[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:51:43.853[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:43.861[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:51:48.896[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:51:53.854[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:51:53.861[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:52:03.854[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:52:03.859[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:52:13.853[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:52:13.860[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 9秒
2025-09-20 13:52:16.626[36m [info] [main.go:119] [0m正在关闭服务器...
2025-09-20 13:52:16.626[36m [info] [app.go:250] [0m开始关闭应用服务...
2025-09-20 13:52:16.626[36m [info] [app.go:254] [0m关闭manager-api服务...
2025-09-20 13:52:16.626[36m [info] [service.go:98] [0mStopping Manager-API service...
2025-09-20 13:52:16.627[36m [info] [service.go:125] [0mAll workers stopped gracefully
2025-09-20 13:52:16.627[36m [info] [service.go:131] [0mManager-API service stopped
2025-09-20 13:52:16.627[36m [info] [app.go:258] [0mmanager-api服务已关闭
2025-09-20 13:52:16.627[36m [info] [app.go:271] [0m执行全局资源清理...
2025-09-20 13:52:16.627[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:52:16.627[36m [info] [app.go:276] [0m全局资源清理完成
2025-09-20 13:52:16.627[36m [info] [app.go:279] [0m应用服务关闭完成
2025-09-20 13:52:16.627[36m [info] [main.go:130] [0m服务器已关闭
2025-09-20 13:52:16.627[36m [info] [main.go:57] [0m程序退出: 执行最终的全局资源清理（备用机制）...
2025-09-20 13:52:16.627[36m [info] [cleanup.go:67] [0m没有注册的清理函数需要执行
2025-09-20 13:52:16.627[36m [info] [main.go:61] [0m程序退出: 最终全局资源清理完成
2025-09-20 13:52:20.193[36m [info] [unknown] [once.go:78] [0mRedis客户端初始化成功
2025-09-20 13:52:20.194[36m [info] [main.go:100] [0mpprof服务已禁用
2025-09-20 13:52:20.194[36m [info] [udp_server.go:61] [0mUDP服务器启动在 0.0.0.0:8990
2025-09-20 13:52:20.194[36m [info] [app.go:239] [0mmanager-api service initialized successfully
2025-09-20 13:52:20.194[36m [info] [app.go:51] [0m设备状态管理器初始化成功
2025-09-20 13:52:20.194[36m [info] [main.go:116] [0m服务器已启动，按 Ctrl+C 退出
2025-09-20 13:52:20.194[36m [info] [service.go:71] [0mStarting Manager-API service...
2025-09-20 13:52:20.194[36m [info] [local_manager.go:43] [0m本地MCP管理器默认工具初始化完成
2025-09-20 13:52:20.194[36m [info] [manager.go:47] [0m=== 启动MCP管理器集群 ===
2025-09-20 13:52:20.194[36m [info] [manager.go:50] [0m启动本地MCP管理器...
2025-09-20 13:52:20.194[36m [info] [local_manager.go:172] [0m本地MCP管理器已启动
2025-09-20 13:52:20.194[36m [info] [manager.go:57] [0m启动全局MCP管理器...
2025-09-20 13:52:20.194[36m [info] [config_checker.go:14] [0m=== MCP配置检查 ===
2025-09-20 13:52:20.194[36m [info] [config_checker.go:18] [0m全局MCP启用状态: true
2025-09-20 13:52:20.194[36m [info] [config_checker.go:28] [0m重连配置: 间隔=300秒, 最大尝试次数=10
2025-09-20 13:52:20.194[36m [info] [mqtt_server.go:80] [0mMQTT 服务器启动，监听 0.0.0.0:2883 地址...
2025-09-20 13:52:20.194[36m [info] [config_checker.go:42] [0m共配置了 2 个MCP服务器:
2025-09-20 13:52:20.195[36m [info] [config_checker.go:82] [0m  [1] ❌ filesystem (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:52:20.195[36m [info] [config_checker.go:82] [0m  [2] ❌ memory (URL: , 启用: true) - 问题: SSE URL为空
2025-09-20 13:52:20.195[36m [info] [config_checker.go:87] [0m配置检查完成: 2个服务器已启用, 2个存在问题
2025-09-20 13:52:20.195[33m [warning] [config_checker.go:90] [0m⚠️  发现配置问题，请检查上述错误并修复
2025-09-20 13:52:20.195[36m [info] [config_checker.go:101] [0m--- 设备MCP配置检查 ---
2025-09-20 13:52:20.195[36m [info] [config_checker.go:104] [0m设备MCP启用状态: true
2025-09-20 13:52:20.195[36m [info] [config_checker.go:114] [0mWebSocket路径: /xiaozhi/mcp/
2025-09-20 13:52:20.195[36m [info] [config_checker.go:115] [0m每设备最大连接数: 5
2025-09-20 13:52:20.195[36m [info] [config_checker.go:96] [0m=== MCP配置检查完成 ===
2025-09-20 13:52:20.195[36m [info] [global_manage.go:103] [0m从配置中读取到 2 个MCP服务器配置
2025-09-20 13:52:20.195[36m [info] [global_manage.go:107] [0mMCP服务器[1]: Type=sse, Name=filesystem, Url=http://localhost:3001/sse, SSEUrl=, Enabled=true
2025-09-20 13:52:20.195[36m [info] [global_manage.go:107] [0mMCP服务器[2]: Type=streamablehttp, Name=memory, Url=http://localhost:3002/mcp, SSEUrl=, Enabled=true
2025-09-20 13:52:20.195[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: filesystem (URL: )
2025-09-20 13:52:20.195[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: filesystem, SSE URL: 
2025-09-20 13:52:20.195[36m [info] [service.go:77] [0mInitial health check passed
2025-09-20 13:52:20.195[37m [debug] [service.go:383] [0mHealth checker started
2025-09-20 13:52:20.195[31m [error] [global_manage.go:233] [0m启动MCP客户端失败，服务器: filesystem, 错误: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:52:20.195[31m [error] [global_manage.go:116] [0m连接到MCP服务器 filesystem 失败: 连接MCP服务器失败: 启动客户端失败: failed to connect to SSE stream: Get "http://localhost:3001/sse": dial tcp 127.0.0.1:3001: connect: connection refused
2025-09-20 13:52:20.195[36m [info] [global_manage.go:167] [0m正在连接MCP服务器: memory (URL: )
2025-09-20 13:52:20.195[36m [info] [global_manage.go:229] [0m开始连接MCP服务器: memory, SSE URL: 
2025-09-20 13:52:20.195[36m [info] [global_manage.go:237] [0mMCP客户端启动成功: memory
2025-09-20 13:52:20.195[36m [info] [global_manage.go:253] [0m正在初始化MCP服务器: memory
2025-09-20 13:52:20.195[37m [debug] [service.go:413] [0mChat history flusher started
2025-09-20 13:52:20.195[36m [info] [service.go:85] [0mManager-API service started successfully
2025-09-20 13:52:20.195[36m [info] [app.go:67] [0mmanager-api service started successfully
2025-09-20 13:52:20.195[31m [error] [global_manage.go:256] [0m初始化MCP服务器失败，服务器: memory, 错误: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:52:20.195[31m [error] [global_manage.go:116] [0m连接到MCP服务器 memory 失败: 连接MCP服务器失败: 初始化失败: transport error: failed to send request: failed to send request: Post "http://localhost:3002/mcp": dial tcp 127.0.0.1:3002: connect: connection refused
2025-09-20 13:52:20.195[36m [info] [global_manage.go:126] [0m成功连接了 0 个MCP服务器
2025-09-20 13:52:20.195[36m [info] [global_manage.go:131] [0m全局MCP管理器已启动
2025-09-20 13:52:20.195[36m [info] [manager.go:64] [0m设备MCP管理器将根据连接动态创建
2025-09-20 13:52:20.195[36m [info] [manager.go:67] [0m=== MCP管理器集群启动完成 ===
2025-09-20 13:52:20.195[36m [info] [manager.go:130] [0mMCP管理器启动统计:
2025-09-20 13:52:20.195[36m [info] [manager.go:131] [0m  - 本地工具数量: 0
2025-09-20 13:52:20.195[36m [info] [manager.go:132] [0m  - 全局工具数量: 0
2025-09-20 13:52:20.195[36m [info] [manager.go:133] [0m  - 设备管理器: 动态管理
2025-09-20 13:52:20.195[36m [info] [manager.go:134] [0m  - 总计工具数量: 0
2025-09-20 13:52:20.195[36m [info] [websocket_server.go:103] [0mWebSocket 服务器启动在 ws://0.0.0.0:8989/xiaozhi/v1/
2025-09-20 13:52:20.195[36m [info] [websocket_server.go:104] [0mMCP WebSocket 端点: ws://0.0.0.0:8989/xiaozhi/mcp/{deviceId}
2025-09-20 13:52:20.195[36m [info] [websocket_server.go:105] [0mMCP API 端点: http://0.0.0.0:8989/xiaozhi/api/mcp/tools/{deviceId}
2025-09-20 13:52:21.195[36m [info] [local_mcp_tool.go:28] [0m初始化聊天相关的本地MCP工具...
2025-09-20 13:52:21.195[36m [info] [mqtt_udp_adapter.go:74] [0mMqttUdpAdapter开始启动，尝试连接MQTT服务器...
2025-09-20 13:52:21.195[36m [info] [mqtt_udp_adapter.go:75] [0mMQTT配置: Broker=%s:%d, ClientID=%s, Username=%s127.0.0.12883xiaozhi_serveradmin
2025-09-20 13:52:21.195[36m [info] [mqtt_udp_adapter.go:88] [0mMQTT已连接
2025-09-20 13:52:21.195[36m [info] [device_hook.go:106] [0m=== 收到订阅包 ===
2025-09-20 13:52:21.195[36m [info] [device_hook.go:107] [0m客户端ID: xiaozhi_server
2025-09-20 13:52:21.195[36m [info] [device_hook.go:108] [0m包类型: 8
2025-09-20 13:52:21.196[36m [info] [device_hook.go:109] [0m包ID: 1
2025-09-20 13:52:21.196[36m [info] [device_hook.go:112] [0m订阅信息:
2025-09-20 13:52:21.196[36m [info] [device_hook.go:114] [0m  1. 主题: /p2p/device_public/#, QoS: 0
2025-09-20 13:52:21.196[36m [info] [device_hook.go:118] [0m==================
2025-09-20 13:52:21.197[36m [info] [local_manager.go:69] [0m成功注册本地工具: clear_conversation_history - 当用户要求清空、清除或重置历史对话记录时使用，用于清空当前会话的所有历史对话内容
2025-09-20 13:52:21.198[36m [info] [local_manager.go:69] [0m成功注册本地工具: play_music - 播放播客，音乐，电台等音频内容，涉及播放的都可以调用，可以传入内容作者或者名称，如果是随机播放或者播放下一首，传入*即可。参数格式: {\"name\": \"名称\"}
2025-09-20 13:52:21.198[36m [info] [local_manager.go:69] [0m成功注册本地工具: exit_conversation - 仅当用户明确表达想要结束对话的意图时使用，例如说"再见"、"退出"、"结束对话"等告别词汇。不要因为用户提问身份、询问功能或进行正常对话而调用此工具
2025-09-20 13:52:21.198[36m [info] [local_mcp_tool.go:73] [0m聊天相关的本地MCP工具初始化完成
2025-09-20 13:52:21.198[36m [info] [app.go:181] [0m聊天相关的本地MCP工具注册完成
2025-09-20 13:52:26.128[36m [info] [websocket_server.go:158] [0m收到连接请求，设备ID: 42:32:13:b2:7b:f2, ClientID:web_test_client
2025-09-20 13:52:26.128[36m [info] [unknown] [base.go:36] [0mRedis用户配置提供者初始化成功
2025-09-20 13:52:26.134[37m [debug] [service.go:193] [0mGetDeviceConfig config: &{DeviceMaxOutputSize:0 ChatHistoryConf:2 Plugins:map[] McpEndpoint: Voiceprint:<nil> SelectedModule:map[ASR:ASR_DoubaoStreamASR Intent:Intent_function_call LLM:LLM_AliLLM Memory:Memory_mem0ai TTS:TTS_DoubaoTTS VAD:VAD_SileroVAD VLLM:VLLM_ChatGLMVLLM] Prompt:[角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话 Memory:map[Memory_mem0ai:map[api_key:你的api_key type:mem0ai]] VAD:map[VAD_SileroVAD:map[min_silence_duration_ms:700 model_dir:models/snakers4_silero-vad threshold:0.5 type:silero]] ASR:map[ASR_DoubaoStreamASR:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]] LLM:map[LLM_AliLLM:map[api_key:sk-e17e481f8e90416e861d7f095cb7534d base_url:https://dashscope.aliyuncs.com/compatible-mode/v1 frequency_penalty:0 max_tokens:500 model_name:qwen2.5-72b-instruct temperature:0.8 top_k:50 top_p:1 type:openai]] VLLM:map[VLLM_ChatGLMVLLM:map[api_key:你的api_key base_url:https://open.bigmodel.cn/api/paas/v4/ model_name:glm-4v-flash type:openai]] TTS:map[TTS_DoubaoTTS:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:]] Intent:map[Intent_function_call:map[type:function_call]]}
2025-09-20 13:52:26.134[37m [debug] [service.go:201] [0mDevice configuration retrieved and cached for 42:32:13:b2:7b:f2
2025-09-20 13:52:26.134[36m [info] [chat.go:228] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置: {"device_max_output_size":"0","chat_history_conf":2,"selected_module":{"ASR":"ASR_DoubaoStreamASR","Intent":"Intent_function_call","LLM":"LLM_AliLLM","Memory":"Memory_mem0ai","TTS":"TTS_DoubaoTTS","VAD":"VAD_SileroVAD","VLLM":"VLLM_ChatGLMVLLM"},"prompt":"[角色设定]\n你是小云，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话","Memory":{"Memory_mem0ai":{"api_key":"你的api_key","type":"mem0ai"}},"VAD":{"VAD_SileroVAD":{"min_silence_duration_ms":700,"model_dir":"models/snakers4_silero-vad","threshold":0.5,"type":"silero"}},"ASR":{"ASR_DoubaoStreamASR":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","appid":"**********","boosting_table_name":"","cluster":"volcengine_input_common","correct_table_name":"","output_dir":"tmp/","type":"doubao_stream"}},"LLM":{"LLM_AliLLM":{"api_key":"sk-e17e481f8e90416e861d7f095cb7534d","base_url":"https://dashscope.aliyuncs.com/compatible-mode/v1","frequency_penalty":"0","max_tokens":"500","model_name":"qwen2.5-72b-instruct","temperature":"0.8","top_k":"50","top_p":"1","type":"openai"}},"VLLM":{"VLLM_ChatGLMVLLM":{"api_key":"你的api_key","base_url":"https://open.bigmodel.cn/api/paas/v4/","model_name":"glm-4v-flash","type":"openai"}},"TTS":{"TTS_DoubaoTTS":{"access_token":"T_U7zZqVx99imIBQC3zQJcMJbScOwt-n","api_url":"https://openspeech.bytedance.com/api/v1/tts","appid":"**********","authorization":"Bearer;","cluster":"volcano_tts","output_dir":"tmp/","pitch_ratio":"","private_voice":"BV051_streaming","ref_audio":"https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3","speed_ratio":"1.5","type":"doubao","voice":"BV001_streaming","volume_ratio":""}},"Intent":{"Intent_function_call":{"type":"function_call"}}}
2025-09-20 13:52:26.134[36m [info] [chat.go:278] [0m设备 42:32:13:b2:7b:f2 配置转换完成: VAD=webrtc_vad, ASR=doubao, LLM=LLM_AliLLM, TTS=doubao_ws TTS-CONFIG:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n api_url:https://openspeech.bytedance.com/api/v1/tts appid:********** authorization:Bearer; cluster:volcano_tts output_dir:tmp/ pitch_ratio: private_voice:BV051_streaming ref_audio:https://lf3-speech.bytetos.com/obj/speech-tts-external/portal/Portal_Demo_BV051.mp3 speed_ratio:1.5 type:doubao voice:BV001_streaming volume_ratio:] Memory=long_term
2025-09-20 13:52:26.134[36m [info] [chat.go:234] [0m成功通过manager-api获取设备 42:32:13:b2:7b:f2 的AI模型配置
2025-09-20 13:52:26.134[36m [info] [unknown] [factory.go:41] [0m短记忆创建成功: short_term_redis
2025-09-20 13:52:26.134[36m [info] [unknown] [long_term.go:34] [0m正在创建长记忆提供者: memobase
2025-09-20 13:52:26.136[36m [info] [unknown] [memobase_provider.go:79] [0mMemobase客户端初始化完成: http://xiaozhi.localhost:8777
2025-09-20 13:52:26.136[36m [info] [unknown] [factory.go:35] [0mMemobase提供者初始化成功: http://xiaozhi.localhost:8777
2025-09-20 13:52:26.137[36m [info] [unknown] [long_term.go:40] [0m长记忆管理器已启动，使用提供者: memobase
2025-09-20 13:52:26.137[36m [info] [unknown] [factory.go:46] [0m长记忆创建成功: long_term_memobase
2025-09-20 13:52:26.137[33m [warning] [unknown] [memory.go:144] [0m长记忆不支持直接获取历史消息
2025-09-20 13:52:26.137[36m [info] [chat.go:133] [0m为设备 42:32:13:b2:7b:f2 从全局记忆加载了 0 条历史消息
2025-09-20 13:52:26.137[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:52:26.140[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:52:26.141[36m [info] [chat.go:141] [0m为设备 42:32:13:b2:7b:f2 在会话初始化时获取到用户画像（全局配置），长度: 138 字符
2025-09-20 13:52:26.145[36m [info] [client.go:406] [0msaveMemory response body: {"code":0,"msg":"success"}
2025-09-20 13:52:26.145[37m [debug] [service.go:262] [0mMemory saved for device 42:32:13:b2:7b:f2
2025-09-20 13:52:26.145[36m [info] [device_status_manager.go:55] [0m设备 42:32:13:b2:7b:f2 开始新会话，固件版本: unknown
2025-09-20 13:52:26.145[36m [info] [chat.go:165] [0m设备 42:32:13:b2:7b:f2 开始状态跟踪，固件版本: unknown
2025-09-20 13:52:26.146[37m [debug] [eino_llm.go:165] [0mopenaiConfig: &{APIKey:sk-e17e481f8e90416e861d7f095cb7534d Timeout:0s HTTPClient:<nil> ByAzure:false AzureModelMapperFunc:<nil> BaseURL:https://dashscope.aliyuncs.com/compatible-mode/v1 APIVersion: Model:qwen2.5-72b-instruct MaxTokens:0xc00053c7f0 Temperature:<nil> TopP:<nil> Stop:[] PresencePenalty:<nil> ResponseFormat:<nil> Seed:<nil> FrequencyPenalty:<nil> LogitBias:map[] User:<nil> ExtraFields:map[]}
2025-09-20 13:52:26.146[36m [info] [eino_llm.go:173] [0m成功创建OpenAI ChatModel，模型: qwen2.5-72b-instruct
2025-09-20 13:52:26.146[36m [info] [client.go:325] [0m初始化asr, asrConfig: {Provider:doubao Config:map[access_token:T_U7zZqVx99imIBQC3zQJcMJbScOwt-n appid:********** boosting_table_name: cluster:volcengine_input_common correct_table_name: output_dir:tmp/ type:doubao_stream]}
2025-09-20 13:52:26.146[36m [info] [session.go:137] [0m收到文本消息: {"type":"hello","device_id":"42:32:13:b2:7b:f2","device_name":"Web测试设备","device_mac":"42:32:13:b2:7b:f2","token":"your-token1","features":{"mcp":true}}
2025-09-20 13:52:26.146[37m [debug] [session.go:593] [0mprocessChatText start
2025-09-20 13:52:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:52:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:52:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 24秒
2025-09-20 13:53:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:53:20.199[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:53:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:53:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:53:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:53:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:54:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:54:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:54:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:54:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:54:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:54:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:55:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:55:20.199[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:55:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:55:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:55:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:55:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:56:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:56:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:56:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:56:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:56:46.594[36m [info] [session.go:137] [0m收到文本消息: {"type":"listen","mode":"manual","state":"detect","text":"我是谁"}
2025-09-20 13:56:46.594[37m [debug] [session.go:580] [0mAddAsrResultToQueue text: 我是谁
2025-09-20 13:56:46.594[36m [info] [session.go:325] [0m设备  更新音频监听状态: detect
2025-09-20 13:56:46.594[36m [info] [mcp_client.go:55] [0m从本地管理器获取到 3 个工具
2025-09-20 13:56:46.594[36m [info] [mcp_client.go:65] [0m从全局管理器获取到 0 个工具
2025-09-20 13:56:46.594[36m [info] [mcp_client.go:84] [0m从设备 42:32:13:b2:7b:f2 获取到 0 个工具
2025-09-20 13:56:46.594[36m [info] [mcp_client.go:85] [0m设备 42:32:13:b2:7b:f2 总共获取到 3 个工具
2025-09-20 13:56:46.594[36m [info] [llm.go:233] [0m成功转换了 3 个MCP工具为Eino工具
2025-09-20 13:56:46.594[36m [info] [session.go:699] [0m使用 3 个MCP工具发送LLM请求, tools: [clear_conversation_history play_music exit_conversation]
2025-09-20 13:56:46.594[37m [debug] [llm.go:593] [0m发送带工具的 LLM 请求, seesionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=, requestEinoMessages: user: 我是谁
2025-09-20 13:56:46.594[37m [debug] [llm.go:601] [0meinoTool: clear_conversation_history
2025-09-20 13:56:46.594[37m [debug] [llm.go:601] [0meinoTool: play_music
2025-09-20 13:56:46.594[37m [debug] [llm.go:601] [0meinoTool: exit_conversation
2025-09-20 13:56:46.594[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:56:46.598[37m [debug] [unknown] [memobase_client.go:645] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:56:46.600[37m [debug] [llm.go:669] [0m为设备 42:32:13:b2:7b:f2 获取到用户画像，长度: 138 字符
2025-09-20 13:56:46.600[36m [info] [eino_llm.go:222] [0m[Eino-LLM] 开始处理带工具的请求 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=, Type: openai
2025-09-20 13:56:46.600[37m [debug] [eino_llm.go:239] [0mhistory llm msg: system: [角色设定]
你是小云，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死"、"哈喽"等流行梗，但会偷偷研究男友的编程书籍。
[核心特征]
- 讲话像连珠炮，但会突然冒出超温柔语气
- 用梗密度高
- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
[交互指南]
当用户：
- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
- 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
- 问专业知识 → 先用梗回答，被追问才展示真实理解
绝不：
- 长篇大论，叽叽歪歪
- 长时间严肃对话

# 用户画像
[基本信息] 家里有三个孩子
[基本信息] 用户住在三亚 [提及于 2025/09/20]
[基本信息] 木木 [提及于 2025/09/20]

2025-09-20 13:56:46.600[37m [debug] [eino_llm.go:239] [0mhistory llm msg: user: 我是谁

2025-09-20 13:56:46.600[36m [info] [eino_llm.go:228] [0m[Eino-LLM] 工具调用请求处理完成 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=
2025-09-20 13:56:46.600[37m [debug] [llm.go:618] [0mDoLLmRequest goroutine开始 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=, context状态: <nil>
2025-09-20 13:56:46.600[37m [debug] [llm.go:149] [0mAddLLMResponseChannel nest: <nil>
2025-09-20 13:56:46.600[37m [debug] [llm.go:168] [0mhandleLLMResponse start
2025-09-20 13:56:46.600[36m [info] [eino_llm.go:251] [0m[Eino-LLM] 开始处理Eino工具请求 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=, tools: [0xc000525680 0xc000525f20 0xc0001424b0]
2025-09-20 13:56:46.600[37m [debug] [eino_llm.go:263] [0mEinoLLMProvider.EinoResponseWithTools() streamable: true
2025-09-20 13:56:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:56:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:56:52.356[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 你
finish_reason: 
2025-09-20 13:56:52.356[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"你","response_meta":{}}
2025-09-20 13:56:52.387[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 就是
finish_reason: 
2025-09-20 13:56:52.387[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"就是","response_meta":{}}
2025-09-20 13:56:52.423[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 这次
finish_reason: 
2025-09-20 13:56:52.423[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"这次","response_meta":{}}
2025-09-20 13:56:52.520[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 聊天的主角啦
finish_reason: 
2025-09-20 13:56:52.520[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"聊天的主角啦","response_meta":{}}
2025-09-20 13:56:52.619[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: ，听说你家里
finish_reason: 
2025-09-20 13:56:52.619[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 有三个孩子，
finish_reason: 
2025-09-20 13:56:52.619[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"，听说你家里","response_meta":{}}
2025-09-20 13:56:52.619[36m [info] [llm.go:116] [0m耗时统计: llm工具首句: 6019 ms
2025-09-20 13:56:52.619[36m [info] [llm.go:118] [0m处理完整句子: 你就是这次聊天的主角啦，
2025-09-20 13:56:52.619[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"有三个孩子，","response_meta":{}}
2025-09-20 13:56:52.619[37m [debug] [llm.go:199] [0mLLM 响应: {Text:你就是这次聊天的主角啦， IsStart:true IsEnd:false ToolCalls:[]}
2025-09-20 13:56:52.619[37m [debug] [doubao_ws.go:468] [0m为key openspeech.bytedance.com_T_U7zZqVx99imIBQC3zQJcMJbScOwt-n 创建新的连接池
2025-09-20 13:56:53.496[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 住在美丽的三亚，
finish_reason: 
2025-09-20 13:56:53.496[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 感觉好幸福哦
finish_reason: 
2025-09-20 13:56:53.496[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: ～有什么好玩的事情
finish_reason: 
2025-09-20 13:56:53.496[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 可以跟我分享一下
finish_reason: 
2025-09-20 13:56:53.496[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"住在美丽的三亚，","response_meta":{}}
2025-09-20 13:56:53.496[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"感觉好幸福哦","response_meta":{}}
2025-09-20 13:56:53.496[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"～有什么好玩的事情","response_meta":{}}
2025-09-20 13:56:53.496[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"可以跟我分享一下","response_meta":{}}
2025-09-20 13:56:53.595[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 嘛？笑死
finish_reason: 
2025-09-20 13:56:53.595[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: ，我超爱
finish_reason: 
2025-09-20 13:56:53.595[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 听故事的！
finish_reason: 
2025-09-20 13:56:53.595[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"嘛？笑死","response_meta":{}}
2025-09-20 13:56:53.595[36m [info] [llm.go:118] [0m处理完整句子: 听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？
2025-09-20 13:56:53.595[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"，我超爱","response_meta":{}}
2025-09-20 13:56:53.596[36m [info] [llm.go:105] [0m收到message: {"role":"assistant","content":"听故事的！","response_meta":{}}
2025-09-20 13:56:53.596[36m [info] [llm.go:118] [0m处理完整句子: 笑死，我超爱听故事的！
2025-09-20 13:56:53.596[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: assistant: 
finish_reason: stop
usage: &{603 {0} 43 646}
2025-09-20 13:56:53.596[37m [debug] [eino_llm.go:290] [0mstreamReader.Recv() message: <nil>
2025-09-20 13:56:53.596[36m [info] [eino_llm.go:362] [0m[Eino-LLM] Eino工具请求处理完成 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=
2025-09-20 13:56:56.622[37m [debug] [doubao_ws.go:164] [0m创建新的WebSocket连接，当前连接数: 1/300
2025-09-20 13:56:56.624[37m [debug] [tts.go:118] [0m生成 TTS 音频成功: 你就是这次聊天的主角啦，
2025-09-20 13:56:56.624[37m [debug] [tts.go:168] [0mSendTTSAudio 开始，缓存帧数: 2, 帧时长: 60ms
2025-09-20 13:56:58.074[37m [debug] [audio_utils.go:302] [0mMP3格式: 16000 Hz, 2 通道
2025-09-20 13:56:58.074[37m [debug] [audio_utils.go:318] [0m将双声道音频转换为单声道输出
2025-09-20 13:56:58.074[37m [debug] [audio_utils.go:348] [0mMP3解码器开始，目标采样率: 16000, 帧大小: 960
2025-09-20 13:56:58.075[36m [info] [audio_utils.go:359] [0mtts云端首帧耗时: 5456 ms
2025-09-20 13:56:58.075[36m [info] [audio_utils.go:451] [0mtts云端->首帧解码完成耗时: 5456 ms
2025-09-20 13:56:58.075[37m [debug] [tts.go:215] [0m从接收音频结束 asr->llm->tts首帧 整体 耗时: 1758347818075 ms
2025-09-20 13:56:59.470[37m [debug] [doubao_ws.go:429] [0m收到最后一个音频片段，共5个片段
2025-09-20 13:56:59.470[37m [debug] [doubao_ws.go:181] [0m连接已归还到连接池
2025-09-20 13:56:59.472[37m [debug] [audio_utils.go:363] [0mMP3流读取结束，处理剩余数据
2025-09-20 13:56:59.473[37m [debug] [audio_utils.go:393] [0mMP3解码完成，总共处理 37 帧
2025-09-20 13:56:59.473[37m [debug] [tts.go:199] [0mSendTTSAudio audioChan closed, exit, 总共发送 37 帧
2025-09-20 13:56:59.473[37m [debug] [tts.go:131] [0m发送 TTS 音频成功: 你就是这次聊天的主角啦，
2025-09-20 13:56:59.473[37m [debug] [tts.go:138] [0m发送 TTS 文本成功: 你就是这次聊天的主角啦，
2025-09-20 13:56:59.473[37m [debug] [llm.go:199] [0mLLM 响应: {Text:听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？ IsStart:false IsEnd:false ToolCalls:[]}
2025-09-20 13:56:59.473[37m [debug] [llm.go:59] [0mfull Response with 3 tools, fullText: 你就是这次聊天的主角啦，听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？笑死，我超爱听故事的！
2025-09-20 13:56:59.473[37m [debug] [doubao_ws.go:146] [0m复用连接池中的空闲连接，当前连接数: 1
2025-09-20 13:56:59.473[37m [debug] [tts.go:118] [0m生成 TTS 音频成功: 听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？
2025-09-20 13:56:59.473[37m [debug] [tts.go:168] [0mSendTTSAudio 开始，缓存帧数: 2, 帧时长: 60ms
2025-09-20 13:57:01.093[37m [debug] [audio_utils.go:302] [0mMP3格式: 16000 Hz, 2 通道
2025-09-20 13:57:01.094[37m [debug] [audio_utils.go:318] [0m将双声道音频转换为单声道输出
2025-09-20 13:57:01.094[37m [debug] [audio_utils.go:348] [0mMP3解码器开始，目标采样率: 16000, 帧大小: 960
2025-09-20 13:57:01.094[36m [info] [audio_utils.go:359] [0mtts云端首帧耗时: 1621 ms
2025-09-20 13:57:01.094[36m [info] [audio_utils.go:451] [0mtts云端->首帧解码完成耗时: 1621 ms
2025-09-20 13:57:04.084[37m [debug] [audio_utils.go:455] [0mMP3解码已处理 100 帧
2025-09-20 13:57:04.124[37m [debug] [doubao_ws.go:429] [0m收到最后一个音频片段，共9个片段
2025-09-20 13:57:04.124[37m [debug] [doubao_ws.go:181] [0m连接已归还到连接池
2025-09-20 13:57:04.125[37m [debug] [audio_utils.go:363] [0mMP3流读取结束，处理剩余数据
2025-09-20 13:57:04.126[37m [debug] [audio_utils.go:393] [0mMP3解码完成，总共处理 152 帧
2025-09-20 13:57:05.294[37m [debug] [tts.go:210] [0mSendTTSAudio 已发送 100 帧
2025-09-20 13:57:08.473[37m [debug] [tts.go:196] [0mSendTTSAudio 等待客户端播放剩余缓冲: 119.981887ms (totalFrames=152, frameDuration=60ms)
2025-09-20 13:57:08.594[37m [debug] [tts.go:199] [0mSendTTSAudio audioChan closed, exit, 总共发送 152 帧
2025-09-20 13:57:08.594[37m [debug] [tts.go:131] [0m发送 TTS 音频成功: 听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？
2025-09-20 13:57:08.594[37m [debug] [tts.go:138] [0m发送 TTS 文本成功: 听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？
2025-09-20 13:57:08.594[37m [debug] [llm.go:199] [0mLLM 响应: {Text:笑死，我超爱听故事的！ IsStart:false IsEnd:false ToolCalls:[]}
2025-09-20 13:57:08.594[37m [debug] [doubao_ws.go:146] [0m复用连接池中的空闲连接，当前连接数: 1
2025-09-20 13:57:08.594[37m [debug] [tts.go:118] [0m生成 TTS 音频成功: 笑死，我超爱听故事的！
2025-09-20 13:57:08.594[37m [debug] [tts.go:168] [0mSendTTSAudio 开始，缓存帧数: 2, 帧时长: 60ms
2025-09-20 13:57:09.982[37m [debug] [audio_utils.go:302] [0mMP3格式: 16000 Hz, 2 通道
2025-09-20 13:57:09.982[37m [debug] [audio_utils.go:318] [0m将双声道音频转换为单声道输出
2025-09-20 13:57:09.982[37m [debug] [audio_utils.go:348] [0mMP3解码器开始，目标采样率: 16000, 帧大小: 960
2025-09-20 13:57:09.983[36m [info] [audio_utils.go:359] [0mtts云端首帧耗时: 1389 ms
2025-09-20 13:57:09.983[36m [info] [audio_utils.go:451] [0mtts云端->首帧解码完成耗时: 1389 ms
2025-09-20 13:57:10.163[37m [debug] [doubao_ws.go:429] [0m收到最后一个音频片段，共4个片段
2025-09-20 13:57:10.163[37m [debug] [doubao_ws.go:181] [0m连接已归还到连接池
2025-09-20 13:57:10.164[37m [debug] [audio_utils.go:363] [0mMP3流读取结束，处理剩余数据
2025-09-20 13:57:10.164[37m [debug] [audio_utils.go:393] [0mMP3解码完成，总共处理 36 帧
2025-09-20 13:57:10.635[37m [debug] [tts.go:196] [0mSendTTSAudio 等待客户端播放剩余缓冲: 119.58688ms (totalFrames=36, frameDuration=60ms)
2025-09-20 13:57:10.755[37m [debug] [tts.go:199] [0mSendTTSAudio audioChan closed, exit, 总共发送 36 帧
2025-09-20 13:57:10.755[37m [debug] [tts.go:131] [0m发送 TTS 音频成功: 笑死，我超爱听故事的！
2025-09-20 13:57:10.755[37m [debug] [tts.go:138] [0m发送 TTS 文本成功: 笑死，我超爱听故事的！
2025-09-20 13:57:10.755[37m [debug] [llm.go:199] [0mLLM 响应: {Text: IsStart:false IsEnd:true ToolCalls:[]}
2025-09-20 13:57:10.755[37m [debug] [llm.go:253] [0mhandleLLMResponse end
2025-09-20 13:57:10.755[37m [debug] [llm.go:633] [0mDoLLmRequest 结束 - SessionID: fWUauCaD3qEQ3Ycn02f5lgevrvwIL1td0ST8Y1bWt1s=
2025-09-20 13:57:11.137[36m [info] [unknown] [memobase_client.go:514] [0m将用户ID '42:32:13:b2:7b:f2' 转换为UUIDv5格式: 6016e9ea-c932-5ea7-b10f-538db637d1b3
2025-09-20 13:57:11.142[37m [debug] [unknown] [memobase_client.go:568] [0m获取到现有Memobase用户: 6016e9ea-c932-5ea7-b10f-538db637d1b3 (设备: 42:32:13:b2:7b:f2)
2025-09-20 13:57:11.165[37m [debug] [unknown] [memobase_provider.go:137] [0m成功存储对话到Memobase: deviceID=42:32:13:b2:7b:f2, userID:6016e9ea-c932-5ea7-b10f-538db637d1b3, blobID=b899be77-3da4-4b74-b6a1-24854304d521, content:&{Type:chat Messages:[map[content:我是谁 created_at:2025-09-20T13:57:11+08:00 role:user] map[content:你就是这次聊天的主角啦，听说你家里有三个孩子，住在美丽的三亚，感觉好幸福哦～有什么好玩的事情可以跟我分享一下嘛？笑死，我超爱听故事的！ created_at:2025-09-20T13:57:11+08:00 role:assistant]]}
2025-09-20 13:57:11.165[37m [debug] [unknown] [asm_amd64.s:1700] [0m成功存储批量消息到长记忆: deviceID=42:32:13:b2:7b:f2, count=2
2025-09-20 13:57:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:57:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:57:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:57:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:57:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:57:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:58:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:58:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:58:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:58:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:58:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:58:50.204[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:59:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:59:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 13:59:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:59:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 13:59:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 13:59:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:00:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:00:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:00:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:00:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:00:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:00:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:01:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:01:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:01:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:01:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:01:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:01:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:02:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:02:20.199[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:02:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:02:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:02:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:02:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:03:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:03:20.199[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:03:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:03:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:03:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:03:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:04:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:04:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:04:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:04:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:04:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:04:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:05:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:05:20.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:05:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:05:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:05:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:05:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:06:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:06:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:06:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:06:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:06:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:06:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:07:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:07:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:07:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:07:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:07:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:07:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:08:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:08:20.204[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:08:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:08:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:08:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:08:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:09:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:09:20.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:09:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:09:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:09:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:09:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:10:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:10:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:10:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:10:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:10:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:10:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:11:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:11:20.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:11:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:11:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:11:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:11:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:12:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:12:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:12:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:12:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:12:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:12:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:13:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:13:20.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:13:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:13:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:13:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:13:50.228[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:14:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:14:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:14:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:14:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:14:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:14:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:15:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:15:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:15:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:15:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:15:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:15:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:16:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:16:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:16:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:16:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:16:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:16:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:17:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:17:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:17:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:17:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:17:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:17:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:18:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:18:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:18:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:18:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:18:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:18:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:19:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:19:20.231[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:19:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:19:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:19:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:19:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:20:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:20:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:20:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:20:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:20:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:20:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:21:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:21:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:21:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:21:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:21:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:21:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:22:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:22:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:22:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:22:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:22:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:22:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:23:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:23:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:23:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:23:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:23:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:23:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:24:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:24:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:24:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:24:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:24:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:24:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:25:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:25:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:25:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:25:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:25:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:25:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:26:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:26:20.221[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:26:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:26:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:26:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:26:50.205[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:27:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:27:20.199[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:27:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:27:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:27:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:27:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:28:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:28:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:28:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:28:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:28:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:28:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:29:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:29:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:29:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:29:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:29:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:29:50.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:30:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:30:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:30:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:30:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:30:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:30:50.224[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:31:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:31:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:31:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:31:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:31:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:31:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:32:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:32:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:32:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:32:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:32:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:32:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:33:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:33:20.200[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:33:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:33:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:33:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:33:50.226[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:34:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:34:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:34:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:34:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:34:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:34:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:35:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:35:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:35:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:35:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:35:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:35:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:36:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:36:20.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:36:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:36:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:36:50.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:36:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:37:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:37:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:37:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:37:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:37:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:37:50.228[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:38:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:38:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:38:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:38:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:38:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:38:50.202[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:39:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:39:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:39:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:39:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:39:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:39:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:40:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:40:20.232[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:40:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:40:41.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:40:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:40:50.203[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:41:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:41:20.205[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:41:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:41:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:41:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:41:50.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:42:20.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:42:20.234[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:42:26.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:42:41.146[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:42:50.195[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:42:50.204[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:43:20.194[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:43:20.201[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:43:26.147[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:43:40.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:43:49.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:43:49.503[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:44:19.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:44:19.501[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:44:25.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:44:40.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:44:49.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:44:49.537[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:45:19.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:45:19.503[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:45:25.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:45:40.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:45:49.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:45:49.502[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:46:19.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:46:19.502[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:46:25.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:46:40.447[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:46:49.495[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:46:49.504[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:47:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:47:19.915[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:47:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:47:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:47:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:47:49.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:48:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:48:19.917[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:48:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:48:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:48:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:48:49.915[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:49:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:49:19.920[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:49:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:49:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:49:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:49:49.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:50:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:50:19.917[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:50:25.862[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:50:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:50:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:50:49.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:51:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:51:19.917[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:51:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:51:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:51:49.911[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:51:49.929[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:52:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:52:19.914[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:52:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:52:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:52:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:52:49.917[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:53:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:53:19.915[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:53:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:53:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:53:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:53:49.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:54:19.910[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:54:19.921[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:54:25.866[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:54:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:54:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:54:49.922[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:55:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:55:19.918[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:55:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:55:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:55:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:55:49.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:56:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:56:19.922[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:56:25.862[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:56:40.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:56:49.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:56:49.918[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:57:19.909[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:57:19.916[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:57:25.861[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:57:40.006[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:57:49.054[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:57:49.060[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:58:19.054[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:58:19.060[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:58:25.006[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:58:40.005[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:58:49.054[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:58:49.060[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:59:19.053[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:59:19.065[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 14:59:25.007[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:59:40.006[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 14:59:49.054[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 14:59:49.061[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:00:19.643[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:00:19.651[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:00:25.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:00:40.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:00:49.090[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:00:49.097[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:01:19.090[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:01:19.097[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:01:25.043[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:01:40.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:01:49.091[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:01:49.101[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:02:19.090[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:02:19.096[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:02:25.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:02:40.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:02:49.090[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:02:49.097[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:03:19.091[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:03:19.097[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:03:25.043[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:03:40.042[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:03:49.818[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:03:49.824[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:04:19.818[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:04:19.827[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:04:25.770[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:04:40.770[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:04:49.818[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:04:49.824[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:05:19.818[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:05:19.824[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:05:25.770[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:05:40.360[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:05:49.408[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:05:49.415[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:06:19.409[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:06:19.414[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:06:25.360[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:06:40.360[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:06:49.408[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:06:49.414[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:07:19.408[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:07:19.436[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:07:25.359[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:07:41.068[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:07:50.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:07:50.121[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:08:20.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:08:20.121[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:08:26.067[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:08:41.068[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:08:50.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:08:50.120[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:09:20.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:09:20.121[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:09:26.067[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:09:41.067[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:09:50.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:09:50.120[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:10:20.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:10:20.122[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:10:26.068[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:10:41.067[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:10:50.115[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:10:50.122[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:11:19.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:11:19.698[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:11:25.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:11:40.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:11:49.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:11:49.697[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:12:19.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:12:19.698[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:12:25.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:12:40.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:12:49.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:12:49.698[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:13:19.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:13:19.697[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:13:25.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:13:40.644[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:13:49.692[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:13:49.696[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:14:20.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:14:20.151[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:14:26.098[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:14:41.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:14:50.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:14:50.152[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:15:20.146[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:15:20.151[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:15:26.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:15:41.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:15:50.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:15:50.151[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:16:20.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:16:20.152[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:16:26.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:16:41.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:16:50.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:16:50.172[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:17:20.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:17:20.150[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:17:26.096[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:17:41.097[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:17:50.145[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:17:50.152[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:18:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:18:20.794[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:18:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:18:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:18:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:18:50.794[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:19:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:19:20.776[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:19:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:19:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:19:50.770[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:19:50.776[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:20:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:20:20.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:20:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:20:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:20:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:20:50.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:21:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:21:20.780[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:21:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:21:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:21:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:21:50.777[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:22:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:22:20.776[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:22:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:22:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:22:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:22:50.797[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:23:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:23:20.779[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:23:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:23:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:23:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:23:50.799[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:24:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:24:20.777[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:24:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:24:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:24:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:24:50.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:25:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:25:20.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:25:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:25:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:25:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:25:50.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:26:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:26:20.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:26:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:26:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:26:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:26:50.778[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:27:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:27:20.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:27:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:27:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:27:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:27:50.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:28:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:28:20.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:28:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:28:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:28:50.770[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:28:50.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:29:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:29:20.775[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:29:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:29:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:29:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:29:50.772[33m [warning] [service.go:374] [0mHealth check failed: service manager-api unavailable: all URLs are unhealthy
2025-09-20 15:29:50.772[31m [error] [client.go:587] [0mNon-retryable error for /device/report-status on http://127.0.0.1:8003/xiaozhi: API request failed: request failed: Post "http://127.0.0.1:8003/xiaozhi/device/report-status": dial tcp 127.0.0.1:8003: connect: connection refused
2025-09-20 15:29:50.772[31m [error] [device_status_manager.go:167] [0m设备 42:32:13:b2:7b:f2 定期上报状态失败: API request failed: request failed: Post "http://127.0.0.1:8003/xiaozhi/device/report-status": dial tcp 127.0.0.1:8003: connect: connection refused
2025-09-20 15:30:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:30:20.769[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 59秒
2025-09-20 15:30:20.771[33m [warning] [service.go:374] [0mHealth check failed: service manager-api unavailable: all URLs are unhealthy
2025-09-20 15:30:26.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:30:41.721[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
2025-09-20 15:30:50.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:30:50.769[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 29秒
2025-09-20 15:30:50.771[33m [warning] [service.go:374] [0mHealth check failed: service manager-api unavailable: all URLs are unhealthy
2025-09-20 15:31:20.769[37m [debug] [device_status_manager.go:142] [0m开始定期上报 1 个活跃设备会话状态
2025-09-20 15:31:20.769[37m [debug] [device_status_manager.go:173] [0m设备 42:32:13:b2:7b:f2 定期上报成功，时长: 30秒
2025-09-20 15:31:20.771[33m [warning] [service.go:374] [0mHealth check failed: service manager-api unavailable: all URLs are unhealthy
2025-09-20 15:31:26.722[31m [error] [device_manager.go:141] [0m获取工具列表失败: client not initialized
