# 服务器配置
server:
  port: 8003
  mode: release  # debug, release, test
  context_path: /xiaozhi
  
# 数据库配置
database:
  host: 127.0.0.1
  port: 3306
  username: root
  password: 123456
  dbname: xiaozhi_esp32_server
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  
# 日志配置
log:
  level: info
  format: json
  
# JWT 配置
jwt:
  secret: xiaozhi-jwt-secret-key
  expire_time: 7200  # 2小时，单位：秒

# 长记忆配置
memory:
  long_term:
    enabled: true
    provider: memobase  # 支持的提供者: memobase, null
    providers:
      memobase:
        project_url: "http://127.0.0.1:8777"  # Memobase项目URL，如: https://your-memobase-instance.com
        api_key: "example"      # Memobase API密钥
        timeout: 30      # 请求超时时间（秒）
        retry_count: 3   # 重试次数