// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/device/admin/page": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员分页查询所有设备信息，包含用户信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "分页查询所有设备",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keywords",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PageResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/bind/{agentId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户在指定智能体下的设备列表。系统密钥可获取所有设备，普通用户只能获取自己的设备",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "获取用户设备列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "智能体ID",
                        "name": "agentId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.DeviceEntity"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "获取用户设备状态并转发到MQTT网关",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "转发POST请求到MQTT网关",
                "parameters": [
                    {
                        "type": "string",
                        "description": "智能体ID",
                        "name": "agentId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求体",
                        "name": "body",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/bind/{agentId}/{deviceCode}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将设备绑定到指定智能体",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "绑定设备",
                "parameters": [
                    {
                        "type": "string",
                        "description": "智能体ID",
                        "name": "agentId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "设备激活码",
                        "name": "deviceCode",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/info/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定设备的详细信息，包含使用时长统计。系统密钥可查看任意设备，普通用户只能查看自己的设备",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "获取设备详细信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "设备ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DeviceWithUserInfoDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "设备不存在",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/manual-add": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员手动添加设备到系统",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "手动添加设备",
                "parameters": [
                    {
                        "description": "设备添加信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeviceManualAddDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/register": {
            "post": {
                "description": "注册新设备到系统",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "注册设备",
                "parameters": [
                    {
                        "description": "设备注册信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeviceRegisterDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/report-status": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "设备上报使用时长和在线状态。支持系统密钥和用户token认证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "上报设备状态",
                "parameters": [
                    {
                        "description": "设备状态信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeviceStatusReportDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上报成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "设备不存在",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/unbind": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "解除设备与用户的绑定关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "解绑设备",
                "parameters": [
                    {
                        "description": "解绑设备信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeviceUnBindDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/device/{id}": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新指定设备的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "device"
                ],
                "summary": "更新设备信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "设备ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "设备更新信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeviceUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.CommonResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "msg": {
                    "type": "string"
                }
            }
        },
        "models.DeviceEntity": {
            "type": "object",
            "properties": {
                "agentId": {
                    "type": "string"
                },
                "alias": {
                    "type": "string"
                },
                "appVersion": {
                    "type": "string"
                },
                "autoUpdate": {
                    "type": "integer"
                },
                "board": {
                    "type": "string"
                },
                "createDate": {
                    "type": "string"
                },
                "creator": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "lastConnectedAt": {
                    "type": "string"
                },
                "macAddress": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "updateDate": {
                    "type": "string"
                },
                "updater": {
                    "type": "integer"
                },
                "usageSeconds": {
                    "description": "新增专门的使用时长统计字段",
                    "type": "integer"
                },
                "userId": {
                    "type": "integer"
                }
            }
        },
        "models.DeviceManualAddDTO": {
            "type": "object",
            "required": [
                "macAddress"
            ],
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 100
                },
                "board": {
                    "type": "string",
                    "maxLength": 50
                },
                "macAddress": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 1
                }
            }
        },
        "models.DeviceRegisterDTO": {
            "type": "object",
            "required": [
                "macAddress"
            ],
            "properties": {
                "macAddress": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 1
                }
            }
        },
        "models.DeviceStatusReportDTO": {
            "type": "object",
            "required": [
                "deviceId",
                "sessionDuration"
            ],
            "properties": {
                "appVersion": {
                    "description": "固件版本号（可选更新）",
                    "type": "string",
                    "maxLength": 20
                },
                "deviceId": {
                    "description": "设备ID",
                    "type": "string"
                },
                "isOnline": {
                    "description": "设备在线状态",
                    "type": "boolean"
                },
                "sessionDuration": {
                    "description": "本次会话使用时长（秒）",
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.DeviceUnBindDTO": {
            "type": "object",
            "required": [
                "deviceId"
            ],
            "properties": {
                "deviceId": {
                    "type": "string"
                }
            }
        },
        "models.DeviceUpdateDTO": {
            "type": "object",
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 100
                },
                "autoUpdate": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "sort": {
                    "type": "integer"
                }
            }
        },
        "models.DeviceWithUserInfoDTO": {
            "type": "object",
            "properties": {
                "agentId": {
                    "type": "string"
                },
                "agentName": {
                    "description": "智能体名称",
                    "type": "string"
                },
                "alias": {
                    "type": "string"
                },
                "appVersion": {
                    "type": "string"
                },
                "board": {
                    "type": "string"
                },
                "createDate": {
                    "type": "string"
                },
                "creator": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "isOnline": {
                    "description": "当前在线状态",
                    "type": "boolean"
                },
                "lastConnectedAt": {
                    "type": "string"
                },
                "macAddress": {
                    "type": "string"
                },
                "offlineDuration": {
                    "description": "离线时长",
                    "type": "string"
                },
                "totalDays": {
                    "description": "注册总天数",
                    "type": "integer"
                },
                "updateDate": {
                    "type": "string"
                },
                "updater": {
                    "type": "integer"
                },
                "usageDuration": {
                    "description": "使用时长统计字段（不映射数据库字段）",
                    "type": "string"
                },
                "usageSeconds": {
                    "description": "累计使用时长(秒)",
                    "type": "integer"
                },
                "userId": {
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "models.PageResult": {
            "type": "object",
            "properties": {
                "currPage": {
                    "type": "integer"
                },
                "list": {
                    "type": "array",
                    "items": {}
                },
                "pageSize": {
                    "type": "integer"
                },
                "totalCount": {
                    "type": "integer"
                },
                "totalPage": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8003",
	BasePath:         "/xiaozhi",
	Schemes:          []string{},
	Title:            "小智设备管理API",
	Description:      "小智ESP32设备管理系统API文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
