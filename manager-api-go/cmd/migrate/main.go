package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"xiaozhi/manager-api-go/internal/models"
)

func main() {
	// 数据库连接配置
	dsn := "root:123456@tcp(127.0.0.1:3306)/xiaozhi_esp32_server?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("开始数据库迁移...")

	// 自动迁移DeviceEntity表结构
	err = db.AutoMigrate(&models.DeviceEntity{})
	if err != nil {
		log.Fatalf("迁移DeviceEntity失败: %v", err)
	}

	fmt.Println("DeviceEntity表迁移完成")

	// 检查usage_seconds字段是否存在
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'ai_device' AND COLUMN_NAME = 'usage_seconds'").Scan(&count).Error
	if err != nil {
		log.Fatalf("检查字段失败: %v", err)
	}

	if count == 0 {
		fmt.Println("usage_seconds字段不存在，正在添加...")
		
		// 添加usage_seconds字段
		err = db.Exec("ALTER TABLE ai_device ADD COLUMN usage_seconds BIGINT DEFAULT 0 COMMENT '累计使用时长(秒)'").Error
		if err != nil {
			log.Fatalf("添加usage_seconds字段失败: %v", err)
		}

		// 初始化现有设备的usage_seconds字段
		err = db.Exec("UPDATE ai_device SET usage_seconds = 0 WHERE usage_seconds IS NULL").Error
		if err != nil {
			log.Fatalf("初始化usage_seconds字段失败: %v", err)
		}

		fmt.Println("usage_seconds字段添加成功")
	} else {
		fmt.Println("usage_seconds字段已存在，跳过添加")
	}

	// 验证迁移结果
	var deviceCount int64
	err = db.Model(&models.DeviceEntity{}).Count(&deviceCount).Error
	if err != nil {
		log.Fatalf("验证迁移失败: %v", err)
	}

	fmt.Printf("数据库迁移完成！当前设备数量: %d\n", deviceCount)

	// 显示表结构信息
	fmt.Println("\n当前ai_device表结构:")
	var columns []struct {
		ColumnName    string `gorm:"column:COLUMN_NAME"`
		DataType      string `gorm:"column:DATA_TYPE"`
		IsNullable    string `gorm:"column:IS_NULLABLE"`
		ColumnDefault string `gorm:"column:COLUMN_DEFAULT"`
		ColumnComment string `gorm:"column:COLUMN_COMMENT"`
	}

	err = db.Raw(`
		SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
		FROM INFORMATION_SCHEMA.COLUMNS 
		WHERE TABLE_SCHEMA = DATABASE() 
		  AND TABLE_NAME = 'ai_device' 
		ORDER BY ORDINAL_POSITION
	`).Scan(&columns).Error

	if err != nil {
		log.Printf("获取表结构信息失败: %v", err)
	} else {
		fmt.Printf("%-20s %-15s %-10s %-15s %s\n", "字段名", "数据类型", "可空", "默认值", "注释")
		fmt.Println(string(make([]byte, 80, 80)))
		for _, col := range columns {
			defaultVal := col.ColumnDefault
			if defaultVal == "" {
				defaultVal = "NULL"
			}
			fmt.Printf("%-20s %-15s %-10s %-15s %s\n", 
				col.ColumnName, col.DataType, col.IsNullable, defaultVal, col.ColumnComment)
		}
	}
}
