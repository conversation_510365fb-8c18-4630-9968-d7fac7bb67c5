package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/glebarez/sqlite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"xiaozhi/manager-api-go/internal/models"
)

// InitDB 初始化数据库连接
func InitDB(config *DatabaseConfig) (*gorm.DB, error) {
	dsn := config.GetDSN()
	dbType := config.GetDatabaseType()

	// GORM 配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
	}

	var db *gorm.DB
	var err error

	switch dbType {
	case "sqlite":
		// 确保SQLite数据库文件目录存在
		if err := ensureSQLiteDir(dsn); err != nil {
			return nil, fmt.Errorf("failed to create sqlite directory: %w", err)
		}

		db, err = gorm.Open(sqlite.Open(dsn), gormConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to connect sqlite database: %w", err)
		}

		// 启用外键约束
		if err := db.Exec("PRAGMA foreign_keys = ON").Error; err != nil {
			log.Printf("Warning: failed to enable foreign keys: %v", err)
		}

		// 启用WAL模式以提高并发性能
		if err := db.Exec("PRAGMA journal_mode = WAL").Error; err != nil {
			log.Printf("Warning: failed to enable WAL mode: %v", err)
		}

	case "mysql":
		fallthrough
	default:
		db, err = gorm.Open(mysql.Open(dsn), gormConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to connect mysql database: %w", err)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %w", err)
	}

	// 获取通用数据库对象 sql.DB ，然后使用其提供的功能
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 设置连接池参数（SQLite通常不需要太多连接）
	if dbType == "sqlite" {
		// SQLite建议使用较少的连接数
		sqlDB.SetMaxIdleConns(1)
		sqlDB.SetMaxOpenConns(1)
	} else {
		// MySQL使用配置的连接数
		sqlDB.SetMaxIdleConns(config.MaxIdleConns)
		sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	}

	// 设置连接可复用的最大时间
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 测试数据库连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 执行数据库迁移
	if err := AutoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to auto migrate: %w", err)
	}

	return db, nil
}

// ensureSQLiteDir 确保SQLite数据库文件目录存在
func ensureSQLiteDir(dbPath string) error {
	dir := filepath.Dir(dbPath)
	if dir == "." {
		return nil // 当前目录，无需创建
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
		log.Printf("Created SQLite database directory: %s", dir)
	}

	return nil
}

// AutoMigrate 执行数据库自动迁移
func AutoMigrate(db *gorm.DB) error {
	log.Println("Starting database auto migration...")

	// 定义所有需要迁移的模型
	models := []interface{}{
		// 系统基础表
		&models.SysUser{},
		&models.SysUserToken{},
		&models.SysParams{},
		&models.SysDictType{},
		&models.SysDictData{},

		// 设备相关表
		&models.DeviceEntity{},

		// AI模型相关表
		&models.AIModelProvider{},
		&models.AIModelConfig{},
		&models.AITTSVoice{},

		// 智能体相关表
		&models.Agent{},
		&models.AgentTemplate{},
		&models.AgentChatHistory{},
		&models.AgentPluginMapping{},
		&models.AgentVoicePrint{},

		// OTA相关表
		&models.OtaEntity{},
	}

	// 执行迁移
	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}
	}

	log.Println("Database auto migration completed successfully")
	return nil
}
