package config

import (
	"fmt"

	"github.com/spf13/viper"
)

// Config 应用配置结构体
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Log      LogConfig      `mapstructure:"log"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Memory   MemoryConfig   `mapstructure:"memory"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port        int    `mapstructure:"port"`
	Mode        string `mapstructure:"mode"`
	ContextPath string `mapstructure:"context_path"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	DBName       string `mapstructure:"dbname"`
	Charset      string `mapstructure:"charset"`
	ParseTime    bool   `mapstructure:"parse_time"`
	Loc          string `mapstructure:"loc"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

// MemoryConfig 长记忆配置
type MemoryConfig struct {
	LongTerm LongTermMemoryConfig `mapstructure:"long_term"`
}

// LongTermMemoryConfig 长期记忆配置
type LongTermMemoryConfig struct {
	Enabled   bool                        `mapstructure:"enabled"`
	Provider  string                      `mapstructure:"provider"`
	Providers LongTermMemoryProviders     `mapstructure:"providers"`
}

// LongTermMemoryProviders 长期记忆提供者配置
type LongTermMemoryProviders struct {
	Memobase MemobaseConfig `mapstructure:"memobase"`
}

// MemobaseConfig Memobase配置
type MemobaseConfig struct {
	ProjectURL  string `mapstructure:"project_url"`
	APIKey      string `mapstructure:"api_key"`
	Timeout     int    `mapstructure:"timeout"`
	RetryCount  int    `mapstructure:"retry_count"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置环境变量前缀
	viper.SetEnvPrefix("XIAOZHI")
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// GetDSN 生成数据库连接字符串
func (db *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		db.Username, db.Password, db.Host, db.Port, db.DBName,
		db.Charset, db.ParseTime, db.Loc)
}
