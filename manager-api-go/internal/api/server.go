package api

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"xiaozhi/manager-api-go/internal/config"
	"xiaozhi/manager-api-go/internal/mcp"
	"xiaozhi/manager-api-go/internal/middleware"
	"xiaozhi/manager-api-go/internal/repository"
	"xiaozhi/manager-api-go/internal/service"
)

// Server 服务器结构体
type Server struct {
	config                 *config.Config
	db                     *gorm.DB
	router                 *gin.Engine
	authMiddleware         *middleware.AuthMiddleware
	serverSecretMiddleware *middleware.ServerSecretMiddleware
	handlers               *Handlers
	deviceHandlers         *DeviceHandlers
	adminHandlers          *AdminHandlers
	modelHandlers          *ModelHandlers
	agentHandlers          *AgentHandlers
	configHandlers         *ConfigHandlers
	timbreHandlers         *TimbreHandlers
	serverManageHandlers   *ServerManageHandlers
	authHandlers           *AuthHandlers
	otaHandlers            *OTAHandlers
	otaMagHandlers         *OTAMagHandlers
	voicePrintHandlers     *VoicePrintHandlers
	mcpHandlers            *MCPHandlers
	memoryHandlers         *MemoryHandlers
}

// NewServer 创建新的服务器实例
func NewServer(cfg *config.Config, db *gorm.DB) *Server {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化仓储层
	userRepo := repository.NewSysUserRepository(db)
	userTokenRepo := repository.NewSysUserTokenRepository(db)
	deviceRepo := repository.NewDeviceRepository(db)
	paramsRepo := repository.NewSysParamsRepository(db)
	dictDataRepo := repository.NewSysDictDataRepository(db)
	dictTypeRepo := repository.NewSysDictTypeRepository(db)
	modelProviderRepo := repository.NewAIModelProviderRepository(db)
	modelConfigRepo := repository.NewAIModelConfigRepository(db)
	ttsVoiceRepo := repository.NewAITTSVoiceRepository(db)
	agentRepo := repository.NewAgentRepository(db)
	agentTemplateRepo := repository.NewAgentTemplateRepository(db)
	agentChatHistRepo := repository.NewAgentChatHistoryRepository(db)
	agentPluginMapRepo := repository.NewAgentPluginMappingRepository(db)
	agentVoicePrintRepo := repository.NewAgentVoicePrintRepository(db)
	otaRepo := repository.NewOtaRepository(db)

	// 初始化服务层
	userService := service.NewSysUserService(userRepo, userTokenRepo)
	paramsService := service.NewSysParamsService(paramsRepo)
	deviceService := service.NewDeviceService(deviceRepo, paramsService)
	dictDataService := service.NewSysDictDataService(dictDataRepo, dictTypeRepo)
	dictTypeService := service.NewSysDictTypeService(dictTypeRepo, dictDataRepo)
	modelProviderService := service.NewAIModelProviderService(modelProviderRepo)
	modelConfigService := service.NewAIModelConfigService(modelConfigRepo, modelProviderRepo)
	ttsVoiceService := service.NewAITTSVoiceService(ttsVoiceRepo)
	timbreService := service.NewTimbreService(ttsVoiceRepo)
	agentService := service.NewAgentService(agentRepo, deviceRepo, agentChatHistRepo, agentPluginMapRepo, modelConfigService, timbreService, deviceService)
	agentTemplateService := service.NewAgentTemplateService(agentTemplateRepo)
	agentChatHistoryService := service.NewAgentChatHistoryService(agentChatHistRepo)
	agentPluginMappingService := service.NewAgentPluginMappingService(agentPluginMapRepo)
	agentVoicePrintService := service.NewAgentVoicePrintService(agentVoicePrintRepo, agentRepo, paramsService)
	configService := service.NewConfigService(paramsRepo, deviceRepo, agentRepo, agentTemplateRepo, modelConfigRepo, modelProviderRepo, ttsVoiceRepo, agentPluginMapRepo)
	serverManageService := service.NewServerManageService(paramsRepo)
	tokenService := service.NewTokenService(userRepo, userTokenRepo)
	captchaService := service.NewCaptchaService()
	otaService := service.NewOtaService(otaRepo)
	memoryService, err := service.NewMemoryService(deviceRepo, cfg)
	if err != nil {
		log.Printf("警告: 初始化长记忆服务失败: %v", err)
	}

	// 初始化处理器
	handlers := NewHandlers(userService)
	handlers.deviceService = deviceService
	deviceHandlers := NewDeviceHandlers(deviceService, paramsService)
	adminHandlers := NewAdminHandlers(paramsService, dictDataService, dictTypeService)
	modelHandlers := NewModelHandlers(modelProviderService, modelConfigService, ttsVoiceService)
	agentHandlers := NewAgentHandlers(agentService, agentTemplateService, agentChatHistoryService, agentPluginMappingService, paramsService)
	configHandlers := NewConfigHandlers(configService)
	timbreHandlers := NewTimbreHandlers(timbreService)
	serverManageHandlers := NewServerManageHandlers(serverManageService)
	authHandlers := NewAuthHandlers(userService, tokenService, captchaService, paramsService, dictDataService)
	otaHandlers := NewOTAHandlers(deviceService, paramsService)
	otaMagHandlers := NewOTAMagHandlers(otaService)
	voicePrintHandlers := NewVoicePrintHandlers(agentVoicePrintService)
	var memoryHandlers *MemoryHandlers
	if memoryService != nil {
		memoryHandlers = NewMemoryHandlers(memoryService, deviceService)
	}

	// 初始化MCP相关组件
	var mcpHandlers *MCPHandlers

	mcpHandlers = NewMCPHandlers(mcp.GlobalConnectionManager, mcp.GlobalWebSocketHandler, "")

	// 初始化认证中间件
	authMiddleware := middleware.NewAuthMiddleware(tokenService, paramsService)

	// 初始化服务器密钥中间件
	serverSecretMiddleware := middleware.NewServerSecretMiddleware(paramsService)

	server := &Server{
		config:                 cfg,
		db:                     db,
		router:                 gin.New(),
		authMiddleware:         authMiddleware,
		serverSecretMiddleware: serverSecretMiddleware,
		handlers:               handlers,
		deviceHandlers:         deviceHandlers,
		adminHandlers:          adminHandlers,
		modelHandlers:          modelHandlers,
		agentHandlers:          agentHandlers,
		configHandlers:         configHandlers,
		timbreHandlers:         timbreHandlers,
		serverManageHandlers:   serverManageHandlers,
		authHandlers:           authHandlers,
		otaHandlers:            otaHandlers,
		otaMagHandlers:         otaMagHandlers,
		voicePrintHandlers:     voicePrintHandlers,
		mcpHandlers:            mcpHandlers,
		memoryHandlers:         memoryHandlers,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 添加中间件
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())

	// 添加非200状态码日志记录中间件
	s.router.Use(middleware.RequestResponseLogger())

	// 添加JSON Long类型转换中间件
	//s.router.Use(middleware.JSONLongConverterMiddleware())

	// 添加CORS中间件
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRF-Token,Authorization,Token")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// API 路由组
	apiGroup := s.router.Group(s.config.Server.ContextPath)
	{
		// 健康检查接口
		apiGroup.GET("/health", s.handlers.healthCheck)

		// 用户认证接口组（公开接口，无需认证）
		userGroup := apiGroup.Group("/user")
		{
			userGroup.GET("/captcha", s.authHandlers.generateCaptcha)
			userGroup.POST("/smsVerification", s.authHandlers.sendSmsVerification)
			userGroup.POST("/login", s.authHandlers.login)
			userGroup.POST("/register", s.authHandlers.register)
			userGroup.PUT("/retrieve-password", s.authHandlers.retrievePassword)
			userGroup.GET("/pub-config", s.authHandlers.getPublicConfig)
		}

		// 需要认证的用户接口
		authUserGroup := apiGroup.Group("/user")
		authUserGroup.Use(s.authMiddleware.Middleware())
		{
			authUserGroup.GET("/info", s.authHandlers.getUserInfo)
			authUserGroup.PUT("/change-password", s.authHandlers.changePassword)
		}

		// 系统管理接口组（保留原有接口）
		sysGroup := apiGroup.Group("/sys")
		{
			// 公开接口
			sysGroup.POST("/login", s.handlers.login)
		}

		// 需要认证的系统接口
		authSysGroup := apiGroup.Group("/sys")
		authSysGroup.Use(s.authMiddleware.Middleware())
		{
			// 用户管理接口
			authSysGroup.GET("/user/info", s.handlers.getUserInfo)
			authSysGroup.POST("/logout", s.handlers.logout)
		}

		// 设备管理接口组（需要认证）
		deviceGroup := apiGroup.Group("/device")
		deviceGroup.Use(s.authMiddleware.Middleware())
		{
			deviceGroup.POST("/register", s.deviceHandlers.registerDevice)
			deviceGroup.POST("/bind/:agentId/:deviceCode", s.deviceHandlers.bindDevice)
			deviceGroup.POST("/bind/:agentId", s.deviceHandlers.forwardToMqttGateway)
			deviceGroup.GET("/bind/:agentId", s.deviceHandlers.getUserDevices)
			deviceGroup.GET("/info/:id", s.deviceHandlers.getDeviceInfo)                 // 获取设备详细信息（支持系统密钥和用户token）
			deviceGroup.POST("/report-status", s.deviceHandlers.reportDeviceStatus)     // 上报设备状态和使用时长（支持系统密钥和用户token）
			deviceGroup.POST("/unbind", s.deviceHandlers.unbindDevice)
			deviceGroup.PUT("/update/:id", s.deviceHandlers.updateDevice)
			deviceGroup.POST("/manual-add", s.deviceHandlers.manualAddDevice)
		}

		// 管理员接口组 - 对应原Java项目的AdminController（需要认证）
		adminGroup := apiGroup.Group("/admin")
		adminGroup.Use(s.authMiddleware.Middleware())
		{
			// 用户管理接口
			adminGroup.GET("/users", s.handlers.pageUsers)
			adminGroup.PUT("/users/:id", s.handlers.resetPassword)
			adminGroup.DELETE("/users/:id", s.handlers.deleteUser)
			adminGroup.PUT("/users/changeStatus/:status", s.handlers.changeUserStatus)

			// 设备管理接口（管理员视角）
			adminGroup.GET("/device/all", s.deviceHandlers.pageDevicesForAdmin)

			// 系统参数管理接口
			paramsGroup := adminGroup.Group("/params")
			{
				paramsGroup.GET("/page", s.adminHandlers.pageParams)
				paramsGroup.GET("/:id", s.adminHandlers.getParam)
				paramsGroup.POST("", s.adminHandlers.saveParam)
				paramsGroup.PUT("", s.adminHandlers.updateParam)
				paramsGroup.POST("/delete", s.adminHandlers.deleteParams)
			}

			// 字典数据管理接口
			dictGroup := adminGroup.Group("/dict")
			{
				dataGroup := dictGroup.Group("/data")
				{
					dataGroup.GET("/page", s.adminHandlers.pageDictData)
					dataGroup.GET("/:id", s.adminHandlers.getDictData)
					dataGroup.POST("/save", s.adminHandlers.saveDictData)
					dataGroup.PUT("/update", s.adminHandlers.updateDictData)
					dataGroup.POST("/delete", s.adminHandlers.deleteDictData)
					dataGroup.GET("/type/:dictType", s.adminHandlers.getDictDataByType)
				}

				// 字典类型分页
				typeGroup := dictGroup.Group("/type")
				{
					typeGroup.GET("/page", s.adminHandlers.pageDictType)
					typeGroup.GET("/:id", s.adminHandlers.getDictType)
					typeGroup.POST("/save", s.adminHandlers.saveDictType)
					typeGroup.PUT("/update", s.adminHandlers.updateDictType)
					typeGroup.POST("/delete", s.adminHandlers.deleteDictType)
				}
			}

			// 服务端管理接口
			serverGroup := adminGroup.Group("/server")
			{
				serverGroup.GET("/server-list", s.serverManageHandlers.getWsServerList)
				serverGroup.POST("/emit-action", s.serverManageHandlers.emitServerAction)
			}
		}

		// 模型管理接口组（需要认证）
		modelGroup := apiGroup.Group("/models")
		modelGroup.Use(s.authMiddleware.Middleware())
		{
			// 模型基本信息接口
			modelGroup.GET("/names", s.modelHandlers.getModelNames)
			modelGroup.GET("/llm/names", s.modelHandlers.getLlmModelNames)

			// 模型配置管理接口
			modelGroup.GET("/list", s.modelHandlers.getModelConfigList)
			modelGroup.GET("/config/:id", s.modelHandlers.getModelConfig)
			modelGroup.DELETE("/config/:id", s.modelHandlers.deleteModelConfig)
			// 与Java一致：支持 /models/{id} 删除
			modelGroup.DELETE("/:id", s.modelHandlers.deleteModelConfig)
			modelGroup.PUT("/enable/:id/:status", s.modelHandlers.enableModelConfig)
			modelGroup.PUT("/default/:id", s.modelHandlers.setDefaultModel)

			// 音色管理接口 - 与Java项目路径保持一致 (具体路由，放在前面)
			modelGroup.GET("/:modelId/voices", s.modelHandlers.getVoiceList)

			// 模型类型相关接口 (与Java项目路径保持一致，但统一参数名避免Gin冲突)
			modelGroup.GET("/:modelId/provideTypes", s.modelHandlers.getModelProviderList)
			modelGroup.POST("/:modelId/:provideCode", s.modelHandlers.addModelConfig)
			modelGroup.PUT("/:modelId/:provideCode/:id", s.modelHandlers.editModelConfig)

			// 根据模型ID获取模型详情 (通配路由，放在最后；统一参数名)
			modelGroup.GET("/:modelId", s.modelHandlers.getModelById)

			// 模型供应器管理接口
			providerGroup := modelGroup.Group("/provider")
			{
				providerGroup.GET("", s.modelHandlers.getProviderListPage)
				providerGroup.POST("", s.modelHandlers.addProvider)
				providerGroup.PUT("", s.modelHandlers.editProvider)
				providerGroup.POST("/delete", s.modelHandlers.deleteProvider)
				providerGroup.GET("/plugin/names", s.modelHandlers.getPluginNameList)
			}
		}

		// 智能体管理接口组（需要认证）
		agentGroup := apiGroup.Group("/agent")
		agentGroup.Use(s.authMiddleware.Middleware())
		{
			agentGroup.GET("/list", s.agentHandlers.getUserAgents)
			agentGroup.GET("/all", s.agentHandlers.adminAgentList)
			agentGroup.GET("/:id", s.agentHandlers.getAgentByID)
			agentGroup.POST("", s.agentHandlers.createAgent)
			agentGroup.PUT("/saveMemory/:macAddress", s.agentHandlers.updateAgentMemoryByMac)
			agentGroup.PUT("/:id", s.agentHandlers.updateAgent)
			agentGroup.DELETE("/:id", s.agentHandlers.deleteAgent)
			agentGroup.GET("/template", s.agentHandlers.getTemplateList)
			agentGroup.GET("/:id/sessions", s.agentHandlers.getAgentSessions)
			agentGroup.GET("/:id/chat-history/:sessionId", s.agentHandlers.getAgentChatHistory)
			agentGroup.GET("/:id/chat-history/user", s.agentHandlers.getRecentlyFiftyByAgentID)
			agentGroup.POST("/audio/:audioId", s.agentHandlers.getAudioID)
			agentGroup.GET("/play/:uuid", s.agentHandlers.playAudio)

			chatHistoryGroup := agentGroup.Group("/chat-history")
			{
				chatHistoryGroup.POST("/report", s.agentHandlers.reportAgentChatHistory)
			}

			// MCP接入点
			mcpGroup := agentGroup.Group("/mcp")
			{
				mcpGroup.GET("/address/:agentId", s.agentHandlers.getAgentMcpAccessAddress)
				mcpGroup.GET("/tools/:agentId", s.agentHandlers.getAgentMcpToolsList)
			}

			// 声纹管理接口 - 与Java项目路径保持一致
			voicePrintGroup := agentGroup.Group("/voice-print")
			{
				voicePrintGroup.POST("", s.voicePrintHandlers.createVoicePrint)
				voicePrintGroup.GET("/list/:id", s.voicePrintHandlers.getVoicePrintList)
				voicePrintGroup.PUT("", s.voicePrintHandlers.updateVoicePrint)
				voicePrintGroup.DELETE("/:id", s.voicePrintHandlers.deleteVoicePrint)
			}
		}

		// 长记忆管理接口组（需要认证）
		if s.memoryHandlers != nil {
			memoryGroup := apiGroup.Group("/memory")
			memoryGroup.Use(s.authMiddleware.Middleware())
			{
				memoryGroup.GET("/device/:deviceId/profiles", s.memoryHandlers.getDeviceProfiles)
				memoryGroup.GET("/device/:deviceId/events", s.memoryHandlers.getDeviceEvents)
				memoryGroup.PUT("/device/:deviceId/profile", s.memoryHandlers.updateDeviceProfile)
				memoryGroup.DELETE("/device/:deviceId/profile/:profileId", s.memoryHandlers.deleteDeviceProfile)
				memoryGroup.GET("/device/:deviceId/context", s.memoryHandlers.getDeviceLongTermContext)
			}
		}

		// 配置管理接口组（使用服务器密钥认证）
		configGroup := apiGroup.Group("/config")
		configGroup.Use(s.serverSecretMiddleware.Middleware())
		{
			configGroup.POST("/server-base", s.configHandlers.getServerConfig)
			configGroup.POST("/agent-models", s.configHandlers.getAgentModels)
		}

		// 音色管理接口组（需要认证）
		timbreGroup := apiGroup.Group("/ttsVoice")
		timbreGroup.Use(s.authMiddleware.Middleware())
		{
			timbreGroup.GET("", s.timbreHandlers.pageTimbre)
			timbreGroup.POST("", s.timbreHandlers.saveTimbre)
			timbreGroup.PUT("/:id", s.timbreHandlers.updateTimbre)
			timbreGroup.POST("/delete", s.timbreHandlers.deleteTimbre)
		}

		// OTA管理接口组（公开接口，无需认证）
		oaGroup := apiGroup.Group("/ota")
		{
			oaGroup.POST("", s.otaHandlers.checkOTAVersion)
			oaGroup.POST("/activate", s.otaHandlers.activateDevice)
			oaGroup.GET("", s.otaHandlers.getOTAStatus)
		}

		// OTA固件管理接口组（需要认证，管理员）
		otaMagGroup := apiGroup.Group("/otaMag")
		otaMagGroup.Use(s.authMiddleware.Middleware())
		{
			otaMagGroup.GET("", s.otaMagHandlers.page)
			otaMagGroup.GET(":id", s.otaMagHandlers.get)
			otaMagGroup.GET("/getDownloadUrl/:id", s.otaMagHandlers.getDownloadUrl)
			otaMagGroup.GET("/download/:uuid", s.otaMagHandlers.download)
			otaMagGroup.POST("", s.otaMagHandlers.save)
			otaMagGroup.DELETE(":id", s.otaMagHandlers.delete)
			otaMagGroup.PUT(":id", s.otaMagHandlers.update)
		}

		log.Printf("📝 正在设置MCP路由...")
		s.setupMCPRoutes(apiGroup)
	}

	// 静态Web服务 - 服务前端页面
	s.setupWebStaticRoutes()
}

// setupWebStaticRoutes 设置静态Web服务路由
func (s *Server) setupWebStaticRoutes() {
	// 检查dist目录是否存在
	distPath := "./dist"
	if _, err := os.Stat(distPath); os.IsNotExist(err) {
		log.Printf("Warning: dist directory not found at %s, web interface will not be available", distPath)
		return
	}

	// 设置静态文件服务 - 直接服务前端编译后的文件
	s.router.Static("/js", distPath+"/js")
	s.router.Static("/css", distPath+"/css")
	s.router.Static("/img", distPath+"/img")
	s.router.Static("/fonts", distPath+"/fonts")
	s.router.Static("/static", distPath+"/static")
	s.router.StaticFile("/favicon.ico", distPath+"/favicon.ico")
	s.router.StaticFile("/service-worker.js", distPath+"/service-worker.js")

	// 设置web-cli前端路径（在NoRoute之前）
	s.setupWebCliRoutes()

	// 处理SPA路由 - 所有非API和非静态资源请求都返回index.html
	s.router.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path

		// 如果是API请求，返回404
		if strings.HasPrefix(path, s.config.Server.ContextPath) {
			c.JSON(http.StatusNotFound, gin.H{
				"code": http.StatusNotFound,
				"msg":  "API not found",
			})
			return
		}

		// 如果是静态资源请求，返回404
		if strings.HasPrefix(path, "/js/") ||
			strings.HasPrefix(path, "/css/") ||
			strings.HasPrefix(path, "/img/") ||
			strings.HasPrefix(path, "/fonts/") ||
			strings.HasPrefix(path, "/static/") ||
			strings.HasSuffix(path, ".js") ||
			strings.HasSuffix(path, ".css") ||
			strings.HasSuffix(path, ".png") ||
			strings.HasSuffix(path, ".jpg") ||
			strings.HasSuffix(path, ".ico") ||
			strings.HasSuffix(path, ".woff") ||
			strings.HasSuffix(path, ".woff2") {
			c.AbortWithStatus(http.StatusNotFound)
			return
		}

		// 其他请求返回index.html (SPA路由)
		c.File(distPath + "/index.html")
	})

	log.Printf("🌐 Web interface available at http://localhost:%d", s.config.Server.Port)
	log.Printf("📁 Static files served from: %s", distPath)
}

// setupWebCliRoutes 设置web-cli前端路径
func (s *Server) setupWebCliRoutes() {
	webCliPath := "./web/web-cli"

	// 检查web-cli目录是否存在
	if _, err := os.Stat(webCliPath); os.IsNotExist(err) {
		log.Printf("Warning: web-cli directory not found at %s, web-cli interface will not be available", webCliPath)
		return
	}

	// 创建web-cli路由组，避免与NoRoute冲突
	webCliGroup := s.router.Group("/web-cli")
	{
		// 设置web-cli静态文件服务
		webCliGroup.Static("/", webCliPath)
	}

	log.Printf("🌐 Web-CLI interface available at http://localhost:%d/web-cli/", s.config.Server.Port)
	log.Printf("📁 Web-CLI files served from: %s", webCliPath)
}

// setupMCPRoutes 设置MCP路由
func (s *Server) setupMCPRoutes(apiGroup *gin.RouterGroup) {
	// 根路径重定向到 /mcp_endpoint/
	s.router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/mcp_endpoint/")
	})

	// MCP接入点路由组
	mcpGroup := apiGroup.Group("/mcp_endpoint")
	{
		// 根路径
		mcpGroup.GET("/", s.mcpHandlers.MCPRoot)

		// 健康检查
		mcpGroup.GET("/health", s.mcpHandlers.HealthCheck)

		// WebSocket端点
		mcpGroup.GET("/mcp/", s.mcpHandlers.WebSocketToolEndpoint)
		mcpGroup.GET("/call/", s.mcpHandlers.WebSocketRobotEndpoint)
	}

	// 打印MCP接入点信息
	s.printMCPEndpointInfo()
}

// printMCPEndpointInfo 打印MCP接入点信息
func (s *Server) printMCPEndpointInfo() {

	localIP := getLocalIP()
	port := s.config.Server.Port

	log.Println("=====下面的地址分别是智控台/单模块MCP接入点地址====")
	log.Printf("智控台MCP参数配置: http://%s:%d%s/mcp_endpoint/health?key=%s",
		localIP, port, s.config.Server.ContextPath, "")

	// 现在直接使用agentID作为token，无需加密
	agentID := "single_module"
	log.Printf("单模块部署MCP接入点: ws://%s:%d%s/mcp_endpoint/mcp/?token=%s",
		localIP, port, s.config.Server.ContextPath, agentID)

	log.Printf("🔌 工具端WebSocket连接示例: ws://%s:%d%s/mcp_endpoint/mcp/?token=YOUR_AGENT_ID",
		localIP, port, s.config.Server.ContextPath)
	log.Printf("🤖 小智端WebSocket连接示例: ws://%s:%d%s/mcp_endpoint/call/?token=YOUR_AGENT_ID",
		localIP, port, s.config.Server.ContextPath)

	log.Println("=====将YOUR_AGENT_ID替换为实际的智能体ID即可使用======")
}

// getLocalIP 获取本地IP地址
func getLocalIP() string {
	return "127.0.0.1"
}

// Run 启动服务器
func (s *Server) Run() error {
	addr := fmt.Sprintf(":%d", s.config.Server.Port)
	log.Printf("Server starting on %s", addr)
	return s.router.Run(addr)
}
