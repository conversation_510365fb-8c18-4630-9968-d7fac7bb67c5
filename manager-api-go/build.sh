#!/bin/bash

# 构建脚本 - 同时编译前端和后端项目
# 作者: AI Assistant
# 用途: 编译manager-web前端项目和manager-api-go后端项目，并整合静态资源

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MANAGER_WEB_DIR="$PROJECT_ROOT/main/manager-web"
MANAGER_API_DIR="$SCRIPT_DIR"

print_info "项目根目录: $PROJECT_ROOT"
print_info "前端项目目录: $MANAGER_WEB_DIR"
print_info "后端项目目录: $MANAGER_API_DIR"

# 检查目录是否存在
check_directories() {
    print_info "检查项目目录..."
    
    if [ ! -d "$MANAGER_WEB_DIR" ]; then
        print_error "前端项目目录不存在: $MANAGER_WEB_DIR"
        exit 1
    fi
    
    if [ ! -d "$MANAGER_API_DIR" ]; then
        print_error "后端项目目录不存在: $MANAGER_API_DIR"
        exit 1
    fi
    
    if [ ! -f "$MANAGER_WEB_DIR/package.json" ]; then
        print_error "前端项目package.json不存在"
        exit 1
    fi
    
    if [ ! -f "$MANAGER_API_DIR/go.mod" ]; then
        print_error "后端项目go.mod不存在"
        exit 1
    fi
    
    print_success "目录检查通过"
}

# 检查依赖工具
check_dependencies() {
    print_info "检查依赖工具..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go"
        exit 1
    fi
    
    print_success "依赖工具检查通过"
    print_info "Node.js 版本: $(node --version)"
    print_info "npm 版本: $(npm --version)"
    print_info "Go 版本: $(go version)"
}

# 清理旧的构建产物
clean_build() {
    print_info "清理旧的构建产物..."
    
    # 清理前端dist目录
    if [ -d "$MANAGER_WEB_DIR/dist" ]; then
        rm -rf "$MANAGER_WEB_DIR/dist"
        print_info "已清理前端dist目录"
    fi
    
    # 清理后端static目录
    if [ -d "$MANAGER_API_DIR/static" ]; then
        rm -rf "$MANAGER_API_DIR/static"
        print_info "已清理后端static目录"
    fi
    
    # 清理后端二进制文件
    if [ -f "$MANAGER_API_DIR/manager-api-go" ]; then
        rm -f "$MANAGER_API_DIR/manager-api-go"
        print_info "已清理后端二进制文件"
    fi
    
    print_success "清理完成"
}

# 构建前端项目
build_frontend() {
    print_info "开始构建前端项目..."
    
    cd "$MANAGER_WEB_DIR"
    
    # 安装依赖
    print_info "安装前端依赖..."
    npm install
    
    # 构建项目
    print_info "构建前端项目..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "$MANAGER_WEB_DIR/dist" ]; then
        print_error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    print_success "前端项目构建完成"
}

# 移动前端静态资源到后端项目
move_static_files() {
    print_info "移动前端静态资源到后端项目..."
    
    # 创建后端static目录
    mkdir -p "$MANAGER_API_DIR/static"

    rm -rf "$MANAGER_API_DIR/dist"
    # 复制dist目录内容到static目录
    mv "$MANAGER_WEB_DIR/dist" "$MANAGER_API_DIR/dist"
    
    # 验证复制结果
    if [ ! -f "$MANAGER_API_DIR/dist/index.html" ]; then
        print_error "静态资源移动失败，index.html不存在"
        exit 1
    fi
    
    print_success "静态资源移动完成"
    print_info "静态资源位置: $MANAGER_API_DIR/dist"
}

# 构建后端项目
build_backend() {
    print_info "开始构建后端项目..."
    
    cd "$MANAGER_API_DIR"
    
    # 下载依赖
    print_info "下载Go模块依赖..."
    go mod download
    
    # 整理依赖
    print_info "整理Go模块..."
    go mod tidy
    
    # 构建项目
    print_info "构建后端项目..."
    go build -o manager-api-go cmd/server/main.go
    
    # 检查构建结果
    if [ ! -f "$MANAGER_API_DIR/manager-api-go" ]; then
        print_error "后端构建失败，二进制文件不存在"
        exit 1
    fi
    
    print_success "后端项目构建完成"
}

# 验证构建结果
verify_build() {
    print_info "验证构建结果..."
    
    # 检查后端二进制文件
    if [ -f "$MANAGER_API_DIR/manager-api-go" ]; then
        print_success "✓ 后端二进制文件存在"
        print_info "  文件大小: $(du -h "$MANAGER_API_DIR/manager-api-go" | cut -f1)"
    else
        print_error "✗ 后端二进制文件不存在"
        exit 1
    fi
    
    # 检查静态资源
    if [ -d "$MANAGER_API_DIR/dist" ] && [ -f "$MANAGER_API_DIR/dist/index.html" ]; then
        print_success "✓ 前端静态资源存在"
        print_info "  静态文件数量: $(find "$MANAGER_API_DIR/dist" -type f | wc -l)"
    else
        print_error "✗ 前端静态资源不存在"
        exit 1
    fi
    
    # 检查配置文件
    if [ -f "$MANAGER_API_DIR/configs/config.yaml" ]; then
        print_success "✓ 配置文件存在"
    else
        print_warning "⚠ 配置文件不存在，请确保configs/config.yaml存在"
    fi
    
    print_success "构建验证完成"
}

# 显示运行说明
show_usage() {
    print_info "构建完成！使用说明："
    echo ""
    echo "1. 启动服务器："
    echo "   cd $MANAGER_API_DIR"
    echo "   ./manager-api-go"
    echo ""
    echo "2. 或者指定配置文件："
    echo "   ./manager-api-go -config configs/config.yaml"
    echo ""
    echo "3. 访问应用："
    echo "   http://localhost:8080 (默认端口，具体端口请查看配置文件)"
    echo ""
    print_warning "注意：请确保configs/config.yaml配置文件正确设置"
}

# 主函数
main() {
    print_info "开始构建 manager-web + manager-api-go 项目"
    echo "=================================================="
    
    # 执行构建步骤
    check_directories
    check_dependencies
    clean_build
    build_frontend
    move_static_files
    build_backend
    verify_build
    
    echo "=================================================="
    print_success "🎉 构建完成！"
    show_usage
}

# 执行主函数
main "$@"
