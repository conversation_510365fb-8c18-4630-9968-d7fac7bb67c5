# 修复说明

## 问题1: mcp_endpoint 路径问题

### 问题描述
访问 `http://localhost:8003/` 时会自动重定向到 `http://localhost:8003/mcp_endpoint/#/home`

### 原因
在 `manager-api-go/internal/api/server.go` 中有根路径重定向：
```go
s.router.GET("/", func(c *gin.Context) {
    c.Redirect(http.StatusFound, "/mcp_endpoint/")
})
```

### 解决方案
注释掉根路径重定向，让前端SPA路由正常工作：
```go
// 注释掉根路径重定向，让前端SPA路由正常工作
// s.router.GET("/", func(c *gin.Context) {
//     c.Redirect(http.StatusFound, "/mcp_endpoint/")
// })
```

## 问题2: 手动添加绑定设备没有关联到agent和用户

### 问题描述
手动绑定设备时，设备没有正确关联到用户

### 原因
在 `ActivateDevice` 方法中只更新了 `agent_id`，没有更新 `user_id`

### 解决方案
1. 修改 `ActivateDevice` 方法签名，添加 `userID` 参数
2. 在更新设备时同时设置 `user_id`、`updater` 字段

### 修改的文件
1. `manager-api-go/internal/repository/device_repository.go`
   - 接口定义：添加 userID 参数
   - 实现方法：更新时设置 user_id 和 updater

2. `manager-api-go/internal/service/device_service.go`
   - 调用 ActivateDevice 时传递 userID 参数

## 测试建议

1. **测试根路径访问**
   - 访问 `http://localhost:8003/` 应该显示前端页面，而不是重定向到 mcp_endpoint

2. **测试设备绑定**
   - 手动添加设备绑定后，检查数据库中设备记录的 user_id 字段是否正确设置
   - 验证设备列表中能正确显示用户关联的设备

## 注意事项

- 这些修改保持了向后兼容性
- OTA激活流程（activateDeviceFromOTA）已经正确处理用户关联，无需修改
- 修改后需要重新编译和部署服务
