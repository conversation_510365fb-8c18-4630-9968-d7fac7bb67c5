export default {
  // 登录页面相关提示文本
  'login.requiredUsername': '用户名不能为空',
  'login.requiredPassword': '密码不能为空',
  'login.requiredCaptcha': '验证码不能为空',
  'login.requiredMobile': '请输入正确的手机号码',
  'login.loginSuccess': '登录成功！',
  
  // HeaderBar组件文本
  'header.smartManagement': '智能体管理',
  'header.modelConfig': '模型配置',
  'header.userManagement': '用户管理',
  'header.otaManagement': 'OTA管理',
  'header.deviceManagement': '设备管理',
  'header.paramDictionary': '参数字典',
  'header.paramManagement': '参数管理',
  'header.dictManagement': '字典管理',

  // McpToolCallDialog组件文本
  'mcpToolCall.title': '工具调用',
  'mcpToolCall.execute': '执行',
  'mcpToolCall.chooseFunction': '1、选择功能',
  'mcpToolCall.searchFunction': '搜索功能',
  'mcpToolCall.noResults': '未找到匹配的功能',
  'mcpToolCall.settings': '2、参数设置',
  'mcpToolCall.inputPlaceholder': '请输入{label}',
  'mcpToolCall.valueRange': '取值范围：{min} - {max}',
  'mcpToolCall.selectPlaceholder': '请选择{label}',
  'mcpToolCall.lightTheme': '浅色主题',
  'mcpToolCall.darkTheme': '深色主题',
  'mcpToolCall.pleaseSelect': '请选择一个功能',
  'mcpToolCall.cancel': '取消',
  'mcpToolCall.requiredField': '请输入{field}',
  'mcpToolCall.minValue': '最小值为{value}',
  'mcpToolCall.maxValue': '最大值为{value}',
  'mcpToolCall.selectTool': '请选择要执行的工具',
  'mcpToolCall.executionResult': '3、执行结果',
  'mcpToolCall.copyResult': '复制结果',
  'mcpToolCall.noResultYet': '暂无执行结果',
  'mcpToolCall.loadingToolList': '正在获取工具列表...',
  
  // 工具名称
  'mcpToolCall.toolName.getDeviceStatus': '查看设备状态',
  'mcpToolCall.toolName.setVolume': '设置音量',
  'mcpToolCall.toolName.setBrightness': '设置亮度',
  'mcpToolCall.toolName.setTheme': '设置主题',
  'mcpToolCall.toolName.takePhoto': '拍照识别',
  'mcpToolCall.toolName.getSystemInfo': '系统信息',
  'mcpToolCall.toolName.reboot': '重启设备',
  'mcpToolCall.toolName.upgradeFirmware': '升级固件',
  'mcpToolCall.toolName.getScreenInfo': '屏幕信息',
  'mcpToolCall.toolName.snapshot': '屏幕截图',
  'mcpToolCall.toolName.previewImage': '预览图片',
  'mcpToolCall.toolName.setDownloadUrl': '设置下载地址',
  
  // 工具分类
  'mcpToolCall.category.audio': '音频',
  'mcpToolCall.category.display': '显示',
  'mcpToolCall.category.camera': '拍摄',
  'mcpToolCall.category.system': '系统',
  'mcpToolCall.category.assets': '资源',
  'mcpToolCall.category.deviceInfo': '设备信息',
  
  // 表格分类和属性
  'mcpToolCall.table.audioSpeaker': '音频扬声器',
  'mcpToolCall.table.screen': '屏幕',
  'mcpToolCall.table.network': '网络',
  'mcpToolCall.table.audioControl': '音频控制',
  'mcpToolCall.table.screenControl': '屏幕控制',
  'mcpToolCall.table.systemControl': '系统控制',
  'mcpToolCall.table.screenInfo': '屏幕信息',
  'mcpToolCall.table.hardwareInfo': '硬件信息',
  'mcpToolCall.table.memoryInfo': '内存信息',
  'mcpToolCall.table.applicationInfo': '应用信息',
  'mcpToolCall.table.networkInfo': '网络信息',
  'mcpToolCall.table.displayInfo': '显示信息',
  'mcpToolCall.table.deviceInfo': '设备信息',
  'mcpToolCall.table.systemInfo': '系统信息',
  // 表格列标题
  'mcpToolCall.table.component': '组件',
  'mcpToolCall.table.property': '属性',
  'mcpToolCall.table.value': '值',
  
  'mcpToolCall.prop.volume': '音量',
  'mcpToolCall.prop.brightness': '亮度',
  'mcpToolCall.prop.theme': '主题',
  'mcpToolCall.prop.type': '类型',
  'mcpToolCall.prop.ssid': 'SSID',
  'mcpToolCall.prop.signalStrength': '信号强度',
  'mcpToolCall.prop.operationResult': '操作结果',
  'mcpToolCall.prop.width': '宽度',
  'mcpToolCall.prop.height': '高度',
  'mcpToolCall.prop.screenType': '类型',
  'mcpToolCall.prop.chipModel': '芯片型号',
  'mcpToolCall.prop.cpuCores': 'CPU核心数',
  'mcpToolCall.prop.chipVersion': '芯片版本',
  'mcpToolCall.prop.flashSize': 'Flash大小',
  'mcpToolCall.prop.minFreeHeap': '最小可用堆',
  'mcpToolCall.prop.applicationName': '应用名称',
  'mcpToolCall.prop.applicationVersion': '应用版本',
  'mcpToolCall.prop.compileTime': '编译时间',
  'mcpToolCall.prop.idfVersion': 'IDF版本',
  'mcpToolCall.prop.macAddress': 'MAC地址',
  'mcpToolCall.prop.ipAddress': 'IP地址',
  'mcpToolCall.prop.wifiName': 'WiFi名称',
  'mcpToolCall.prop.wifiChannel': 'WiFi信道',
  'mcpToolCall.prop.screenSize': '屏幕尺寸',
  'mcpToolCall.prop.deviceUuid': '设备UUID',
  'mcpToolCall.prop.systemLanguage': '系统语言',
  'mcpToolCall.prop.currentOtaPartition': '当前OTA分区',
  'mcpToolCall.prop.getResult': '获取结果',
  'mcpToolCall.prop.url': 'URL',
  'mcpToolCall.prop.quality': '质量',
  'mcpToolCall.prop.question': '问题',
  
  // 工具帮助文本
  'mcpToolCall.help.getDeviceStatus': '查看设备的当前运行状态，包括音量、屏幕、电池等信息。',
  'mcpToolCall.help.setVolume': '调整设备的音量大小，请输入0-100之间的数值。',
  'mcpToolCall.help.setBrightness': '调整设备屏幕的亮度，请输入0-100之间的数值。',
  'mcpToolCall.help.setTheme': '切换设备屏幕的显示主题，可以选择浅色或深色模式。',
  'mcpToolCall.help.takePhoto': '使用设备摄像头拍摄照片并进行识别分析，请输入要询问的问题。',
  'mcpToolCall.help.getSystemInfo': '获取设备的系统信息，包括硬件规格、软件版本等。',
  'mcpToolCall.help.reboot': '重启设备，执行后设备将重新启动。',
  'mcpToolCall.help.upgradeFirmware': '从指定URL下载并升级设备固件，升级后设备会自动重启。',
  'mcpToolCall.help.getScreenInfo': '获取屏幕的详细信息，如分辨率、尺寸等参数。',
  'mcpToolCall.help.snapshot': '对当前屏幕进行截图并上传到指定URL。',
  'mcpToolCall.help.previewImage': '在设备屏幕上预览指定URL的图片。',
  'mcpToolCall.help.setDownloadUrl': '设置设备资源文件的下载地址。',
  
  // 其他文本
  'mcpToolCall.text.strong': '强',
  'mcpToolCall.text.medium': '中',
  'mcpToolCall.text.weak': '弱',
  'mcpToolCall.text.dark': '深色',
  'mcpToolCall.text.light': '浅色',
  'mcpToolCall.text.setSuccess': '设置成功',
  'mcpToolCall.text.setFailed': '设置失败',
  'mcpToolCall.text.brightnessSetSuccess': '亮度设置成功',
  'mcpToolCall.text.brightnessSetFailed': '亮度设置失败',
  'mcpToolCall.text.themeSetSuccess': '主题设置成功',
  'mcpToolCall.text.themeSetFailed': '主题设置失败',
  'mcpToolCall.text.rebootCommandSent': '重启指令已发送',
  'mcpToolCall.text.rebootFailed': '重启失败',
  'mcpToolCall.text.monochrome': '单色屏',
  'mcpToolCall.text.color': '彩色屏',
  'mcpToolCall.text.getSuccessParseFailed': '获取成功，但解析失败',
  'mcpToolCall.text.getFailed': '获取失败',
  'mcpToolCall.text.getSuccessFormatError': '获取成功，但数据格式异常',

  // 字典数据对话框相关
  'dictDataDialog.addDictData': '新增字典数据',
  'dictDataDialog.dictLabel': '字典标签',
  'dictDataDialog.dictLabelPlaceholder': '请输入字典标签',
  'dictDataDialog.dictValue': '字典值',
  'dictDataDialog.dictValuePlaceholder': '请输入字典值',
  'dictDataDialog.sort': '排序',
  'dictDataDialog.requiredDictLabel': '请输入字典标签',
  'dictDataDialog.requiredDictValue': '请输入字典值',

  // 字典类型对话框相关
  'dictTypeDialog.addDictType': '新增字典类型',
  'dictTypeDialog.dictName': '字典类型名称',
  'dictTypeDialog.dictNamePlaceholder': '请输入字典类型名称',
  'dictTypeDialog.dictType': '字典类型编码',
  'dictTypeDialog.dictTypePlaceholder': '请输入字典类型编码',
  'dictTypeDialog.requiredDictName': '请输入字典类型名称',
  'dictTypeDialog.requiredDictType': '请输入字典类型编码',

  // 音频编辑对话框相关
  'editVoiceDialog.voiceCode': '音色编码',
  'editVoiceDialog.voiceCodePlaceholder': '请输入音色编码',
  'editVoiceDialog.voiceName': '音色名称',
  'editVoiceDialog.voiceNamePlaceholder': '请输入音色名称',
  'editVoiceDialog.languageType': '语言类型',
  'editVoiceDialog.languageTypePlaceholder': '请输入语言类型',
  'editVoiceDialog.sortNumber': '排序号',
  'editVoiceDialog.remark': '备注',
  'editVoiceDialog.remarkPlaceholder': '请输入备注内容',
  'editVoiceDialog.generatePreview': '生成试听',
  'editVoiceDialog.defaultVoiceName': '湾湾小何',
  'editVoiceDialog.defaultLanguageType': '中文',
  'editVoiceDialog.requiredVoiceCode': '请输入音色编码',
  'editVoiceDialog.requiredVoiceName': '请输入音色名称',

  // 固件对话框相关
  'firmwareDialog.firmwareName': '固件名称',
  'firmwareDialog.firmwareNamePlaceholder': '请输入固件名称(板子+版本号)',
  'firmwareDialog.firmwareType': '固件类型',
  'firmwareDialog.firmwareTypePlaceholder': '请选择固件类型',

  // 声纹对话框相关
  'voicePrintDialog.addSpeaker': '添加说话人',
  'voicePrintDialog.voicePrintVector': '声纹向量',
  'voicePrintDialog.selectVoiceMessage': '请选择一条语言消息',
  'voicePrintDialog.name': '姓名',
  'voicePrintDialog.enterName': '请输入姓名',
  'voicePrintDialog.description': '描述',
  'voicePrintDialog.enterDescription': '请输入描述',
  'voicePrintDialog.save': '保存',
  'voicePrintDialog.cancel': '取消',
  'voicePrintDialog.requiredDescription': '请输入描述',
  'voicePrintDialog.requiredName': '请输入姓名',
  'voicePrintDialog.requiredAudioVector': '请选择音频向量',

  // 声纹页面相关
  'voicePrint.pageTitle': '声纹识别',
  'voicePrint.name': '姓名',
  'voicePrint.description': '描述',
  'voicePrint.createTime': '创建时间',
  'voicePrint.action': '操作',
  'voicePrint.edit': '编辑',
  'voicePrint.delete': '删除',
  'voicePrint.add': '新增',
  'voicePrint.addSpeaker': '添加说话人',
  'voicePrint.editSpeaker': '编辑说话人',
  'voicePrint.fetchFailed': '获取声纹列表失败',
  'voicePrint.updateSuccess': '修改成功',
  'voicePrint.addSuccess': '新增成功',
  'voicePrint.confirmDelete': '确定要删除选中的此声纹吗？',
  'voicePrint.warning': '警告',
  'voicePrint.confirm': '确定',
  'voicePrint.cancel': '取消',
  'voicePrint.deleteSuccess': '成功删除此声纹',
  'voicePrint.deleteFailed': '删除失败，请重试',
  'voicePrint.cancelDelete': '已取消删除操作',
  'voicePrint.closeOperation': '操作已关闭',
  'voicePrint.loading': '拼命加载中',

  // 手動添加設備對話框相關
  'manualAddDeviceDialog.title': '手动添加设备',

  // AddModelDialog组件相关
  'addModelDialog.requiredSupplier': '请选择供应器',
  
  // 注册页面相关
  'register.title': '创建账号',
  'register.welcome': '欢迎使用!',
  'register.usernamePlaceholder': '请输入用户名',
  'register.mobilePlaceholder': '请输入手机号码',
  'register.captchaPlaceholder': '请输入验证码',
  'register.mobileCaptchaPlaceholder': '请输入短信验证码',
  'register.passwordPlaceholder': '请设置密码',
  'register.confirmPasswordPlaceholder': '请确认密码',
  'register.goToLogin': '已有账号？登录',
  'register.registerButton': '注册',
  'register.agreeTo': '注册即表示您同意我们的',
  'register.userAgreement': '用户协议',
  'register.privacyPolicy': '隐私政策',
  'register.notAllowRegister': '当前不允许用户注册',
  'register.captchaLoadFailed': '验证码加载失败',
  'register.inputCaptcha': '请输入验证码',
  'register.inputCorrectMobile': '请输入正确的手机号码',
  'register.captchaSendSuccess': '验证码发送成功',
  'register.captchaSendFailed': '验证码发送失败',
  'register.passwordsNotMatch': '两次输入的密码不一致',
  'register.registerSuccess': '注册成功！',
  'register.registerFailed': '注册失败',
  'register.requiredUsername': '用户名不能为空',
  'register.requiredPassword': '密码不能为空',
  'register.requiredCaptcha': '验证码不能为空',
  'register.requiredMobileCaptcha': '请输入短信验证码',
  
  'manualAddDeviceDialog.deviceType': '设备型号',
  'manualAddDeviceDialog.deviceTypePlaceholder': '请选择设备型号',
  'manualAddDeviceDialog.firmwareVersion': '固件版本',
  'manualAddDeviceDialog.firmwareVersionPlaceholder': '请输入固件版本',
  'manualAddDeviceDialog.macAddress': 'Mac地址',
  'manualAddDeviceDialog.macAddressPlaceholder': '请输入Mac地址',
  'manualAddDeviceDialog.confirm': '确定',
  'manualAddDeviceDialog.cancel': '取消',
  'manualAddDeviceDialog.requiredMacAddress': '请输入Mac地址',

  // 参数对话框相关
  'paramDialog.paramCode': '参数编码',
  'paramDialog.paramCodePlaceholder': '请输入参数编码',
  'paramDialog.paramValue': '参数值',
  'paramDialog.paramValuePlaceholder': '请输入参数值',
  'paramDialog.valueType': '值类型',
  'paramDialog.valueTypePlaceholder': '请选择值类型',
  'paramDialog.remark': '备注',
  'paramDialog.remarkPlaceholder': '请输入备注',
  'paramDialog.save': '保存',
  'paramDialog.cancel': '取消',
  'paramDialog.requiredParamCode': '请输入参数编码',
  'paramDialog.requiredParamValue': '请输入参数值',
  'paramDialog.requiredValueType': '请选择值类型',
  'paramDialog.stringType': '字符串(string)',
  'paramDialog.numberType': '数字(number)',
  'paramDialog.booleanType': '布尔值(boolean)',
  'paramDialog.arrayType': '数组(array)',
  'paramDialog.jsonType': 'JSON对象(json)',
  'manualAddDeviceDialog.invalidMacAddress': '请输入正确的Mac地址格式，例如：00:1A:2B:3C:4D:5E',
  'manualAddDeviceDialog.requiredDeviceType': '请选择设备型号',
  'manualAddDeviceDialog.requiredFirmwareVersion': '请输入固件版本',
  'manualAddDeviceDialog.getFirmwareTypeFailed': '获取固件类型失败',
  'manualAddDeviceDialog.addSuccess': '设备添加成功',
  'manualAddDeviceDialog.addFailed': '添加失败',
  'firmwareDialog.version': '版本号',
  'firmwareDialog.versionPlaceholder': '请输入版本号(x.x.x格式)',
  'firmwareDialog.firmwareFile': '固件文件',
  'firmwareDialog.clickUpload': '点击上传',
  'firmwareDialog.uploadTip': '只能上传固件文件(.bin/.apk)，且不超过100MB',
  'firmwareDialog.uploadHint': '温馨提示：请上传合并前的xiaozhi.bin文件，而不是合并后的merged-binary.bin文件',
  'firmwareDialog.remark': '备注',
  'firmwareDialog.remarkPlaceholder': '请输入备注信息',
  'firmwareDialog.requiredFirmwareName': '请输入固件名称(板子+版本号)',
  'firmwareDialog.requiredFirmwareType': '请选择固件类型',
  'firmwareDialog.requiredVersion': '请输入版本号',
  'firmwareDialog.versionFormatError': '版本号格式不正确，请输入x.x.x格式',
  'firmwareDialog.requiredFirmwareFile': '请上传固件文件',
  'firmwareDialog.invalidFileType': '只能上传.bin/.apk格式的固件文件!',
  'firmwareDialog.invalidFileSize': '固件文件大小不能超过100MB!',
  'firmwareDialog.uploadSuccess': '固件文件上传成功',
  'firmwareDialog.uploadFailed': '文件上传失败',
  'header.providerManagement': '字段管理',
  'header.serverSideManagement': '服务端管理',
  'header.changePassword': '修改密码',
  'header.logout': '退出登录',
  'header.searchPlaceholder': '输入名称搜索..',

  // 登录页面文本
  'login.title': '登录',
  'login.welcome': 'WELCOME TO LOGIN',
  'login.username': '用户名',
  'login.usernamePlaceholder': '请输入用户名',
  'login.mobilePlaceholder': '请输入手机号码',
  'login.password': '密码',
  'login.passwordPlaceholder': '请输入密码',
  'login.captchaPlaceholder': '请输入验证码',
  'login.loginButton': '登录',
  'login.login': '登录',
  'login.register': '新用户注册',
  'login.forgotPassword': '忘记密码',
  'login.forgetPassword': '忘记密码?',
  'login.mobileLogin': '手机号码登录',
  'login.usernameLogin': '用户名登录',
  'login.agreeTo': '登录即同意',
  'login.userAgreement': '《用户协议》',
  'login.and': '和',
  'login.privacyPolicy': '《隐私政策》',
  'login.registerAccount': '注册账号',

  // 注册页面文本
  'register.title': '注册账号',
  'register.welcome': '欢迎注册',
  'register.username': '用户名',
  'register.usernamePlaceholder': '请输入用户名',
  'register.mobile': '手机号',
  'register.mobilePlaceholder': '请输入手机号码',
  'register.password': '密码',
  'register.passwordPlaceholder': '请输入密码',
  'register.confirmPassword': '确认密码',
  'register.confirmPasswordPlaceholder': '请确认密码',
  'register.captcha': '验证码',
  'register.captchaPlaceholder': '请输入验证码',
  'register.mobileCaptcha': '手机验证码',
  'register.mobileCaptchaPlaceholder': '请输入手机验证码',
  'register.getMobileCaptcha': '获取验证码',
  'register.registerButton': '注册',
  'register.login': '登录',
  'register.inputCorrectMobile': '请输入正确的手机号码',
  'register.inputCaptcha': '请输入图形验证码',
  'register.captchaSendSuccess': '验证码发送成功',
  'register.invalidCaptcha': '验证码不正确',
  'register.passwordsNotMatch': '两次输入的密码不一致',
  'register.passwordLength': '密码长度必须在6-20位之间',
  'register.usernameRequired': '用户名不能为空',
  'register.usernameFormat': '用户名格式不正确',
  'register.mobileRequired': '手机号不能为空',
  'register.passwordRequired': '密码不能为空',
  'register.confirmPasswordRequired': '确认密码不能为空',
  'register.goToLogin': '已有账号，去登录',
  'register.agreeTo': '注册即同意',
  'register.userAgreement': '《用户协议》',
  'register.and': '和',
  'register.privacyPolicy': '《隐私政策》',
  'register.sendCaptcha': '获取验证码',
  'register.notAllowRegister': '当前不允许普通用户注册',
  'register.captchaLoadFailed': '验证码加载失败，点击刷新',
  'register.inputCorrectMobile': '请输入正确的手机号码',
  'register.inputCaptcha': '请输入图形验证码',
  'register.captchaSendSuccess': '验证码发送成功',
  'register.captchaSendFailed': '验证码发送失败',
  'register.registerSuccess': '注册成功！',
  'register.registerFailed': '注册失败',
  'register.passwordsNotMatch': '两次输入的密码不一致',

  // 忘记密码页面文本
  'retrievePassword.title': '重置密码',
  'retrievePassword.welcome': '密码找回',
  'retrievePassword.mobile': '手机号',
  'retrievePassword.mobilePlaceholder': '请输入手机号码',
  'retrievePassword.captcha': '验证码',
  'retrievePassword.captchaPlaceholder': '请输入验证码',
  'retrievePassword.mobileCaptcha': '手机验证码',
  'retrievePassword.mobileCaptchaPlaceholder': '请输入手机验证码',
  'retrievePassword.newPassword': '新密码',
  'retrievePassword.newPasswordPlaceholder': '请输入新密码',
  'retrievePassword.confirmNewPassword': '确认新密码',
  'retrievePassword.confirmNewPasswordPlaceholder': '请确认新密码',
  'retrievePassword.getMobileCaptcha': '获取验证码',
  'retrievePassword.updateButton': '立即修改',
  'retrievePassword.goToLogin': '返回登录',
  'retrievePassword.inputCorrectMobile': '请输入正确的手机号码',
  'retrievePassword.captchaSendSuccess': '验证码发送成功',
  'retrievePassword.passwordsNotMatch': '两次输入的密码不一致',
  'retrievePassword.passwordLength': '密码长度必须在6-20位之间',
  'retrievePassword.mobileRequired': '手机号不能为空',
  'retrievePassword.captchaRequired': '验证码不能为空',
  'retrievePassword.mobileCaptchaRequired': '手机验证码不能为空',
  'retrievePassword.newPasswordRequired': '新密码不能为空',
  'retrievePassword.confirmNewPasswordRequired': '确认新密码不能为空',
  'retrievePassword.passwordUpdateSuccess': '密码修改成功',

  // 修改密码页面文本
  'changePassword.title': '修改密码',
  'changePassword.oldPasswordLabel': '旧密码：',
  'changePassword.oldPasswordPlaceholder': '请输入旧密码',
  'changePassword.newPasswordLabel': '新密码：',
  'changePassword.newPasswordPlaceholder': '请输入新密码',
  'changePassword.confirmPasswordLabel': '确认新密码：',
  'changePassword.confirmPasswordPlaceholder': '请再次输入新密码',
  'changePassword.confirmButton': '确定',
  'changePassword.cancelButton': '取消',
  'changePassword.allFieldsRequired': '请填写所有字段',
  'changePassword.passwordsNotMatch': '两次输入的新密码不一致',
  'changePassword.newPasswordSameAsOld': '新密码不能与旧密码相同',
  'changePassword.passwordChangedSuccessfully': '密码修改成功，请重新登录',
  'changePassword.changeFailed': '密码修改失败',

  // 设备管理页面文本
  'device.management': '设备管理',
  'device.add': '添加设备',
  'device.edit': '编辑设备',
  'device.delete': '删除设备',
  'device.name': '设备名称',
  'device.type': '设备类型',
  'device.status': '设备状态',
  'device.dialogTitle': '添加设备',
  'device.verificationCode': '验证码：',
  'device.verificationCodePlaceholder': '请输入设备播报的6位数验证码..',
  'device.confirmButton': '确定',
  'device.cancelButton': '取消',
  'device.input6DigitCode': '请输入6位数字验证码',
  'device.bindSuccess': '设备绑定成功',
  'device.bindFailed': '绑定失败',
  // DeviceManagement页面扩展翻译
  'device.searchPlaceholder': '请输入设备型号或Mac地址查询',
  'device.model': '设备型号',
  'device.macAddress': 'Mac地址',
  'device.firmwareVersion': '固件版本',
  'device.bindTime': '绑定时间',
  'device.lastConversation': '最后对话',
  'device.remark': '备注',
  'device.autoUpdate': '自动升级',
  'device.operation': '操作',
  'device.search': '搜索',
  'device.selectAll': '全选/取消全选',
  'deviceManagement.loading': '拼命加载中',
  'device.bindWithCode': '6位验证码绑定',
  'device.manualAdd': '手动添加',
  'device.unbind': '解绑',
  'device.toolCall': '工具调用',
  'device.selectAtLeastOne': '请至少选择一条记录',
  'device.confirmBatchUnbind': '确认要解绑选中的 {count} 台设备吗？',
  'device.batchUnbindSuccess': '成功解绑 {count} 台设备',
  'device.batchUnbindError': '批量解绑过程中出现错误',
  'device.remarkTooLong': '备注不能超过 64 字符',
  'device.remarkSaved': '备注已保存',
  'device.remarkSaveFailed': '备注保存失败',
  'device.confirmUnbind': '确认要解绑该设备吗？',
  'device.unbindSuccess': '设备解绑成功',
  'device.unbindFailed': '设备解绑失败',
  'device.getListFailed': '获取设备列表失败',
  'device.autoUpdateEnabled': '已设置成自动升级',
  'device.autoUpdateDisabled': '已关闭自动升级',
  'device.batchUnbindSuccess': '成功解绑 {count} 台设备',
  'device.getFirmwareTypeFailed': '获取固件类型失败',
  'device.deviceStatus': '状态',
  'device.online': '在线',
  'device.offline': '离线',

  // 消息提示
  'message.success': '操作成功',
  'message.error': '操作失败',
  'message.warning': '警告',
  'message.info': '提示',

  // 缓存相关
  'cache.viewer': '缓存查看器',

  // 聊天历史对话框相关
  'chatHistory.with': '与',
  'chatHistory.dialogTitle': '的聊天记录',
  'chatHistory.loading': '加载中...',
  'chatHistory.noMoreRecords': '没有更多记录了',
  'chatHistory.selectSession': '请选择会话查看聊天记录',
  'chatHistory.today': '今天',
  'chatHistory.yesterday': '昨天',
  'cache.status': '缓存状态',
  'cache.cdnEnabled': 'CDN模式已启用',
  'cache.cdnDisabled': 'CDN模式已禁用',
  'cache.serviceWorkerRegistered': 'Service Worker已注册',
  'cache.serviceWorkerNotRegistered': 'Service Worker未注册',
  'cache.noCacheDetected': '还未检测到缓存，请刷新页面或等待缓存建立',
  'cache.swDevEnvWarning': '在开发环境中，Service Worker可能无法正常初始化缓存',
  'cache.swCheckMethods': '请尝试以下方法检查Service Worker是否生效:',
  'cache.swCheckMethod1': '在开发者工具的Application/Application标签页中查看Service Worker状态',
  'cache.swCheckMethod2': '在开发者工具的Application/Cache/Cache Storage中查看缓存内容',
  'cache.swCheckMethod3': '使用生产构建(npm run build)并通过HTTP服务器访问以测试完整功能',
  'cache.swDevEnvNormal': '在开发环境中，这是正常现象',
  'cache.swProdOnly': 'Service Worker通常只在生产环境中生效',
  'cache.swTestingTitle': '要测试Service Worker功能:',
  'cache.swTestingStep1': '运行npm run build构建生产版本',
  'cache.swTestingStep2': '通过HTTP服务器访问构建后的页面',
  'cache.swNotSupported': '当前浏览器不支持Service Worker，CDN资源缓存功能不可用',
  'cache.dialogTitle': 'CDN资源缓存状态',
  'cache.loading': '正在加载缓存信息...',
  'cache.notSupported': '您的浏览器不支持Cache API或Service Worker未安装',
  'cache.refreshPage': '刷新页面',
  'cache.noCachedResources': '未发现缓存的CDN资源',
  'cache.noCachedResourcesDesc': 'Service Worker可能尚未完成初始化或缓存尚未建立。请刷新页面或等待一会后再试。',
  'cache.cdnCacheStatus': 'CDN资源缓存状态',
  'cache.totalCachedResources': '共发现 {count} 个缓存资源',
  'cache.jsResources': 'JavaScript 资源 ({count})',
  'cache.cssResources': 'CSS 资源 ({count})',
  'cache.cached': '已缓存',
  'cache.notCached': '未缓存',
  'cache.refreshStatus': '刷新缓存状态',
  'cache.clearCache': '清除缓存',
  'cache.refreshingStatus': '正在刷新缓存状态',
  'cache.confirmClear': '确定要清除所有缓存吗?',
  'cache.clearedSuccess': '缓存已清除',
  'cache.clearFailed': '清除缓存失败',
  'cache.clearCanceled': '已取消清除',

  // 通用按钮
  'button.ok': '确定',
  'button.cancel': '取消',
  'button.save': '保存',
  'button.close': '关闭',

  // 系统信息
  'system.name': '小智服务',

  // 声纹相关
  'voiceprint.management': '声纹管理',
  'voiceprint.add': '添加声纹',
  'voiceprint.delete': '删除声纹',

  // 字典管理页面文本
  'dictManagement.pageTitle': '字典管理',
  'dictManagement.searchPlaceholder': '请输入字典值标签查询',
  'dictManagement.search': '搜索',
  'dictManagement.dictTypeName': '字典类型名称',
  'dictManagement.operation': '操作',
  'dictManagement.edit': '编辑',
  'dictManagement.dictLabel': '字典标签',
  'dictManagement.dictValue': '字典值',
  'dictManagement.sort': '排序',
  'dictManagement.delete': '删除',
  'dictManagement.selectAll': '全选',
  'dictManagement.deselectAll': '取消全选',
  'dictManagement.addDictType': '新增字典类型',
  'dictManagement.batchDeleteDictType': '批量删除字典类型',
  'dictManagement.addDictData': '新增字典数据',
  'dictManagement.batchDeleteDictData': '批量删除字典数据',
  'dictManagement.itemsPerPage': '{items}条/页',
  'dictManagement.firstPage': '首页',
  'dictManagement.prevPage': '上一页',
  'dictManagement.nextPage': '下一页',
  'dictManagement.totalRecords': '共{total}条记录',
  'dictManagement.editDictType': '编辑字典类型',
  'dictManagement.editDictData': '编辑字典数据',
  'dictManagement.saveSuccess': '保存成功',
  'dictManagement.deleteSuccess': '删除成功',
  'dictManagement.selectDictTypeToDelete': '请选择要删除的字典类型',
  'dictManagement.confirmDeleteDictType': '确定要删除选中的字典类型吗?',
  'dictManagement.confirm': '确定',
  'dictManagement.cancel': '取消',
  'dictManagement.selectDictTypeFirst': '请先选择字典类型',
  'dictManagement.confirmDeleteDictData': '确定要删除该字典数据吗?',
  'dictManagement.selectDictDataToDelete': '请选择要删除的字典数据',
  'dictManagement.confirmBatchDeleteDictData': '确定要删除选中的{count}个字典数据吗?',
  'dictManagement.getDictDataFailed': '获取字典数据失败',

  // 用户信息
  'user.info': '用户信息',
  'user.username': '用户名',
  'user.mobile': '手机号',
  // 字段管理页面文本
  'providerManagement.categoryFilter': '类别筛选',
  'providerManagement.searchPlaceholder': '请输入供应器名称查询',
  'providerManagement.category': '类别',
  'providerManagement.providerCode': '供应器编码',
  'providerManagement.fieldConfig': '字段配置',
  'providerManagement.selectToDelete': '请先选择需要删除的供应器',
  'providerManagement.confirmDelete': '确定要删除选中的{count}个供应器吗？',
  'providerManagement.viewFields': '查看字段',
  // 公共文本
  'common.all': '全部',
  'common.search': '搜索',
  'common.name': '名称',
  'common.sort': '排序',
  'common.action': '操作',
  'common.edit': '编辑',
  'common.delete': '删除',
  'common.selectAll': '全选',
  'common.deselectAll': '取消全选',
  'common.add': '新增',
  'common.perPage': '{number}条/页',
  'common.firstPage': '首页',
  'common.prevPage': '上一页',
  'common.nextPage': '下一页',
  'common.totalRecords': '共{number}条记录',
  'common.addProvider': '新增供应器',
  'common.editProvider': '编辑供应器',
  'common.updateSuccess': '修改成功',
  'common.addSuccess': '新增成功',
  'common.deleteSuccess': '删除成功',
  'common.deleteFailure': '删除失败，请重试',
  'common.deleteCancelled': '已取消删除',
  'common.warning': '警告',
  'common.confirm': '确定',
  'common.cancel': '取消',
  'common.sensitive': '敏感',
  'user.userid': '用户Id',
  'user.deviceCount': '设备数量',
  'user.createDate': '注册时间',
  'user.status': '状态',
  'user.normal': '正常',
  'user.disabled': '禁用',
  'user.resetPassword': '重置密码',
  'user.disableAccount': '禁用账户',
  'user.enableAccount': '恢复账号',
  'user.deleteUser': '删除用户',
  'user.deselectAll': '取消全选',
  'user.selectAll': '全选',
  'user.enable': '启用',
  'user.disable': '禁用',
  'user.delete': '删除',
  'user.selectUsersFirst': '请先选择需要删除的用户',
  'user.confirmDeleteSelected': '确定要删除选中的{count}个用户吗？',
  'user.deleting': '正在删除中...',
  'user.deleteSuccess': '成功删除{count}个用户',
  'user.deleteFailed': '删除失败，请重试',
  'user.partialDelete': '成功删除{successCount}个用户，{failCount}个删除失败',
  'user.deleteError': '删除过程中发生错误',
  'user.deleteCancelled': '已取消删除',
  'user.confirmResetPassword': '重置后将会生成新密码，是否继续？',
  'user.resetPasswordSuccess': '密码已重置，请通知用户使用新密码登录',
  'user.confirmDeleteUser': '确定要删除该用户吗？',
  'user.deleteUserSuccess': '删除成功',
  'user.operationFailed': '操作失败，请重试',
  'user.confirmStatusChange': '确定要{action}选中的{count}个用户吗？',
  'user.statusChangeSuccess': '成功{action}{count}个用户',
  'user.invalidUserId': '存在无效的用户ID',
  'user.searchPhone': '请输入手机号码查询',
  'user.search': '搜索',

  // 语言切换
  'language.zhCN': '中文简体',
  'language.zhTW': '中文繁體',
  'language.en': 'English',

  // 首页文本
  'home.addAgent': '添加智能体',
  'home.greeting': '你好!',
  'home.wish': '让我们度过美好的一天！',
  'home.languageModel': '语言模型',
  'home.voiceModel': '音色模型',
  'home.configureRole': '配置角色',
  'home.voiceprintRecognition': '声纹识别',
  'home.deviceManagement': '设备管理',
  'home.chatHistory': '聊天记录',
  'home.lastConversation': '最近对话',
  'home.noConversation': '暂未对话',

  // 参数管理页面文本
  'paramManagement.pageTitle': '参数管理',
  'paramManagement.searchPlaceholder': '请输入参数编码或备注查询',
  'paramManagement.search': '搜索',
  'paramManagement.paramCode': '参数编码',
  'paramManagement.paramValue': '参数值',
  'paramManagement.remark': '备注',
  'paramManagement.operation': '操作',
  'paramManagement.hide': '隐藏',
  'paramManagement.view': '查看',
  'paramManagement.deselectAll': '取消全选',
  'paramManagement.selectAll': '全选',
  'paramManagement.add': '新增',
  'paramManagement.delete': '删除',
  'paramManagement.edit': '编辑',
  'paramManagement.itemsPerPage': '条/页',
  'paramManagement.firstPage': '首页',
  'paramManagement.prevPage': '上一页',
  'paramManagement.nextPage': '下一页',
  'paramManagement.totalRecords': '共{total}条记录',
  'paramManagement.addParam': '新增参数',
  'paramManagement.editParam': '编辑参数',
  'paramManagement.getParamsListFailed': '获取参数列表失败',
  'paramManagement.selectParamsFirst': '请先选择需要删除的参数',
  'paramManagement.confirmBatchDelete': '确定要删除选中的{paramCount}个参数吗？',
  'paramManagement.invalidParamId': '存在无效的参数ID',
  'paramManagement.batchDeleteSuccess': '成功删除{paramCount}个参数',
  'paramManagement.deleteFailed': '删除失败，请重试',
  'paramManagement.operationCancelled': '已取消删除操作',
  'paramManagement.operationClosed': '操作已关闭',
  'paramManagement.updateSuccess': '修改成功',
  'paramManagement.addSuccess': '新增成功',
  'paramManagement.updateFailed': '更新失败',
  'paramManagement.addFailed': '新增失败',
  'home.justNow': '刚刚',
  'home.minutesAgo': '{minutes}分钟前',
  'home.hoursAgo': '{hours}小时{minutes}分钟前',
  'home.confirmDeleteAgent': '确定要删除该智能体吗？',
  'home.deleteSuccess': '删除成功',
  'home.deleteFailed': '删除失败',
  'home.enableMemory': '请先在“配置角色”界面开启记忆',

  // 服务端管理页面文本
  'serverSideManager.pageTitle': '服务端管理',
  'serverSideManager.wsAddress': 'ws地址',
  'serverSideManager.operation': '操作',
  'serverSideManager.restart': '重启',
  'serverSideManager.updateConfig': '更新配置',
  'serverSideManager.restartServer': '重启服务端',
  'serverSideManager.updateConfigTitle': '更新配置',
  'serverSideManager.confirmRestart': '确定要重启服务端吗？',
  'serverSideManager.confirmUpdateConfig': '确定要更新配置吗？',
  'serverSideManager.loading': '拼命加载中',
  'serverSideManager.getServerListFailed': '获取服务端列表失败',
  'serverSideManager.operationFailed': '操作失败',
  'serverSideManager.restartSuccess': '重启成功',
  'serverSideManager.updateConfigSuccess': '更新配置成功',

  // 添加智能体对话框文本
  'addAgentDialog.title': '添加智能体',
  'addAgentDialog.agentName': '智能体名称',
  'addAgentDialog.placeholder': '请输入智能体名称..',
  'addAgentDialog.confirm': '确定',
  'addAgentDialog.cancel': '取消',
  'addAgentDialog.nameRequired': '请输入智能体名称',
  'addAgentDialog.addSuccess': '添加成功',

  // 角色配置页面文本
  'roleConfig.title': '角色配置',
  'roleConfig.restartNotice': '保存配置后，需要重启设备，新的配置才会生效。',
  'roleConfig.saveConfig': '保存配置',
  'roleConfig.reset': '重置',
  'roleConfig.agentName': '助手昵称',
  'roleConfig.roleTemplate': '角色模版',
  'roleConfig.roleIntroduction': '角色介绍',
  'roleConfig.languageCode': '语言编码',
  'roleConfig.interactionLanguage': '交互语种',
  'roleConfig.vad': '语音活动检测(VAD)',
  'roleConfig.asr': '语音识别(ASR)',
  'roleConfig.llm': '大语言模型(LLM)',
  'roleConfig.vllm': '视觉大模型(VLLM)',
  'roleConfig.intent': '意图识别(Intent)',
  'roleConfig.memoryHis': '记忆',
  'roleConfig.memory': '记忆模式',
  'roleConfig.tts': '语音合成(TTS)',
  'roleConfig.voiceType': '声音音色(Voice)',
  'roleConfig.pleaseEnterContent': '请输入内容',
  'roleConfig.pleaseEnterLangCode': '请输入语言编码，如：zh_CN',
  'roleConfig.pleaseEnterLangName': '请输入交互语种，如：中文',
  'roleConfig.pleaseSelect': '请选择',
  'roleConfig.editFunctions': '编辑功能',
  'roleConfig.reportText': '上报文字',
  'roleConfig.reportTextVoice': '上报文字+语音',
  'roleConfig.saveSuccess': '配置保存成功',
  'roleConfig.saveFailed': '配置保存失败',
  'roleConfig.confirmReset': '确定要重置配置吗？',
  'roleConfig.resetSuccess': '配置已重置',
  'roleConfig.fetchTemplatesFailed': '获取模板列表失败',
  'roleConfig.templateApplied': '模板已应用',
  'roleConfig.applyTemplateFailed': '应用模板失败',
  'roleConfig.fetchConfigFailed': '获取配置失败',
  'roleConfig.fetchModelsFailed': '获取模型列表失败',
  'roleConfig.fetchPluginsFailed': '获取插件列表失败',

  // 功能管理对话框文本
  'functionDialog.title': '功能管理',
  'functionDialog.unselectedFunctions': '未选功能',
  'functionDialog.selectedFunctions': '已选功能',
  'functionDialog.selectAll': '全选',
  'functionDialog.noMorePlugins': '没有更多的插件了',
  'functionDialog.pleaseSelectPlugin': '请选择插件功能',
  'functionDialog.paramConfig': '参数配置',
  'functionDialog.noNeedToConfig': ' 无需配置参数',
  'functionDialog.pleaseSelectFunctionForParam': '请选择已配置的功能进行参数设置',
  'functionDialog.mcpAccessPoint': 'MCP接入点',
  'functionDialog.mcpAddressDesc': '以下是智能体的MCP接入点地址。',
  'functionDialog.howToDeployMcp': '如何部署MCP接入点',
  'functionDialog.howToIntegrateMcp': '如何接入MCP功能',
  'functionDialog.copy': '复制',
  'functionDialog.accessPointStatus': '接入点状态',
  'functionDialog.connected': '已连接',
  'functionDialog.loading': '加载中...',
  'functionDialog.disconnected': '未连接',
  'functionDialog.refresh': '刷新',
  'functionDialog.noAvailableTools': '暂无可用工具',
  'functionDialog.cancel': '取消',
  'functionDialog.saveConfig': '保存配置',
  'functionDialog.copiedToClipboard': '已复制到剪贴板',
  'functionDialog.copyFailed': '复制失败，请手动复制',
  'functionDialog.jsonFormatError': '的字段格式错误：JSON格式有误',
  'functionDialog.defaultValue': '默认值',

  // 模型配置页面文本
  'modelConfig.searchPlaceholder': '请输入模型名称查询',
  'modelConfig.search': '搜索',
  'modelConfig.vad': '语言活动检测',
  'modelConfig.asr': '语音识别',
  'modelConfig.llm': '大语言模型',
  'modelConfig.vllm': '视觉大语言模型',
  'modelConfig.intent': '意图识别',
  'modelConfig.tts': '语音合成',
  'modelConfig.memory': '记忆',
  'modelConfig.modelId': '模型ID',
  'modelConfig.modelName': '模型名称',
  'modelConfig.provider': '提供商',
  'modelConfig.unknown': '未知',
  'modelConfig.isEnabled': '是否启用',
  'modelConfig.isDefault': '是否默认',
  'modelConfig.action': '操作',
  'modelConfig.voiceManagement': '音色管理',
  'modelConfig.edit': '修改',
  'modelConfig.duplicate': '创建副本',
  'modelConfig.delete': '删除',
  'modelConfig.deselectAll': '取消全选',
  'modelConfig.select': '选择',
  'modelConfig.selectAll': '全选',
  'modelConfig.add': '新增',
  'modelConfig.selectModelsFirst': '请先选择要删除的模型',
  'modelConfig.confirmBatchDelete': '确定要删除选中的模型吗?',
  'modelConfig.confirmDelete': '确定要删除该模型吗?',
  'modelConfig.batchDeleteSuccess': '批量删除成功',
  'modelConfig.partialDeleteFailed': '部分删除失败',
  'modelConfig.deleteSuccess': '删除成功',
  'modelConfig.deleteFailed': '删除失败',
  'modelConfig.duplicateSuccess': '创建副本成功',
  'modelConfig.duplicateFailed': '创建副本失败',
  'modelConfig.saveSuccess': '保存成功',
  'modelConfig.saveFailed': '保存失败',
  'modelConfig.addSuccess': '新增成功',
  'modelConfig.addFailed': '新增失败',
  'modelConfig.fetchModelsFailed': '获取模型列表失败',
  'modelConfig.enableSuccess': '启用成功',
  'modelConfig.disableSuccess': '禁用成功',
  'modelConfig.operationFailed': '操作失败',
  'modelConfig.setDefaultSuccess': '设置默认模型成功',
  'modelConfig.itemsPerPage': '{items}条/页',
  'modelConfig.firstPage': '首页',
  'modelConfig.prevPage': '上一页',
  'modelConfig.nextPage': '下一页',
  'modelConfig.totalRecords': '共{total}条记录',
  'modelConfig.loading': '拼命加载中',

  // 模型配置对话框文本
  'modelConfigDialog.addModel': '添加模型',
  'modelConfigDialog.editModel': '修改模型',
  'modelConfigDialog.duplicateModel': '创建副本',
  'modelConfigDialog.copySuffix': '_副本',
  'modelConfigDialog.modelInfo': '模型信息',
  'modelConfigDialog.enable': '是否启用',
  'modelConfigDialog.setDefault': '设为默认',
  'modelConfigDialog.modelName': '模型名称',
  'modelConfigDialog.enterModelName': '请输入模型名称',
  'modelConfigDialog.modelCode': '模型编码',
  'modelConfigDialog.enterModelCode': '请输入模型编码',
  'modelConfigDialog.supplier': '供应器',
  'modelConfigDialog.selectSupplier': '请选择',
  'modelConfigDialog.sortOrder': '排序号',
  'modelConfigDialog.enterSortOrder': '请输入排序号',
  'modelConfigDialog.docLink': '文档地址',
  'modelConfigDialog.enterDocLink': '请输入文档地址',
  'modelConfigDialog.remark': '备注',
  'modelConfigDialog.enterRemark': '请输入模型备注',
  'modelConfigDialog.callInfo': '调用信息',
  'modelConfigDialog.enterJsonExample': '请输入JSON格式变量(示例:{"key":"value"})',
  'modelConfigDialog.save': '保存',

  // TTS模型配置文本
  'ttsModel.select': '选择',
  'ttsModel.voiceCode': '音色编码',
  'ttsModel.voiceName': '音色名称',
  'ttsModel.languageType': '语言类型',
  'ttsModel.preview': '试听',
  'ttsModel.enterMp3Url': '请输入MP3地址',
  'ttsModel.remark': '备注',
  'ttsModel.enterRemark': '这里是备注',
  'ttsModel.referenceAudioPath': '克隆音频路径',
  'ttsModel.enterReferenceAudio': '这里是克隆音频路径',
  'ttsModel.referenceText': '克隆音频文本',
  'ttsModel.enterReferenceText': '这里是克隆音频对应文本',
  'ttsModel.action': '操作',
  'ttsModel.operation': '操作',
  'ttsModel.operationFailed': '操作失败',
  'ttsModel.operationClosed': '操作已关闭',
  'ttsModel.edit': '编辑',
  'ttsModel.delete': '删除',
  'ttsModel.save': '保存',
  'ttsModel.deselectAll': '取消全选',
  'ttsModel.selectAll': '全选',
  'ttsModel.add': '新增',
  'ttsModel.fetchVoicesFailed': '获取音色列表失败',
  'ttsModel.loadVoicesFailed': '加载音色数据失败',
  'ttsModel.unnamedVoice': '未命名音色',
  'ttsModel.finishEditingFirst': '请先完成当前编辑',
  'ttsModel.selectVoiceToDelete': '请选择要删除的音色',

  // OTA管理页面文本
  'otaManagement.firmwareManagement': '固件管理',
  'otaManagement.searchPlaceholder': '请输入固件名称查询',
  'otaManagement.search': '搜索',
  'otaManagement.firmwareName': '固件名称',
  'otaManagement.firmwareType': '固件类型',
  'otaManagement.version': '版本号',
  'otaManagement.fileSize': '文件大小',
  'otaManagement.remark': '备注',
  'otaManagement.createTime': '创建时间',
  'otaManagement.updateTime': '更新时间',
  'otaManagement.action': '操作',
  'otaManagement.download': '下载',
  'otaManagement.edit': '编辑',
  'otaManagement.delete': '删除',
  'otaManagement.selectAll': '全选',
  'otaManagement.deselectAll': '取消全选',
  'otaManagement.addNew': '新增',
  'otaManagement.addFirmware': '新增固件',
  'otaManagement.editFirmware': '编辑固件',
  'otaManagement.itemsPerPage': '{items}条/页',
  'otaManagement.firstPage': '首页',
  'otaManagement.prevPage': '上一页',
  'otaManagement.nextPage': '下一页',
  'otaManagement.totalRecords': '共{total}条记录',
  'otaManagement.selectFirmwareFirst': '请先选择需要删除的固件',
  'otaManagement.confirmBatchDelete': '确定要删除选中的{paramCount}个固件吗？',
  'otaManagement.operationCancelled': '已取消删除操作',
  'otaManagement.operationClosed': '操作已关闭',
  'otaManagement.batchDeleteSuccess': '成功删除{paramCount}个固件',
  'otaManagement.deleteFailed': '删除失败，请重试',
  'otaManagement.incompleteFirmwareInfo': '固件信息不完整',
  'otaManagement.getDownloadUrlFailed': '获取下载链接失败',
  'otaManagement.getFirmwareTypesFailed': '获取固件类型失败',
  'otaManagement.updateSuccess': '修改成功',
  'otaManagement.updateFailed': '修改失败',
  'otaManagement.addSuccess': '新增成功',
  'otaManagement.addFailed': '新增失败',
  'otaManagement.fetchFirmwareListFailed': '获取固件列表失败',

  // 字段管理页面模型类型翻译
  'providerManagement.all': '全部',
  'providerManagement.modelType.ASR': '语音识别',
  'providerManagement.modelType.TTS': '语音合成',
  'providerManagement.modelType.LLM': '大语言模型',
  'providerManagement.modelType.VLLM': '视觉大语言模型',
  'providerManagement.modelType.Intent': '意图识别',
  'providerManagement.modelType.Memory': '记忆模块',
  'providerManagement.modelType.VAD': '语音活动检测',
  'providerManagement.modelType.Plugin': '插件工具',

  // Provider Dialog 翻译
  'providerDialog.category': '类别',
  'providerDialog.selectCategory': '请选择类别',
  'providerDialog.code': '编码',
  'providerDialog.inputCode': '请输入供应器编码',
  'providerDialog.name': '名称',
  'providerDialog.inputName': '请输入供应器名称',
  'providerDialog.sort': '排序',
  'providerDialog.fieldConfig': '字段配置',
  'providerDialog.add': '添加',
  'providerDialog.deselectAll': '取消全选',
  'providerDialog.selectAll': '全选',
  'providerDialog.batchDelete': '批量删除',
  'providerDialog.fieldKey': '字段key',
  'providerDialog.fieldLabel': '字段标签',
  'providerDialog.fieldType': '字段类型',
  'providerDialog.type': '类型',
  'providerDialog.stringType': '字符串',
  'providerDialog.numberType': '数字',
  'providerDialog.booleanType': '布尔值',
  'providerDialog.dictType': '字典',
  'providerDialog.arrayType': '分号分割的列表',
  'providerDialog.defaultValue': '默认值',
  'providerDialog.inputDefaultValue': '请输入默认值',
  'providerDialog.operation': '操作',
  'providerDialog.edit': '编辑',
  'providerDialog.complete': '完成',
  'providerDialog.delete': '删除',
  'providerDialog.save': '保存',
  'providerDialog.requiredCategory': '请选择类别',
  'providerDialog.requiredCode': '请输入供应器编码',
  'providerDialog.requiredName': '请输入供应器名称',
  'providerDialog.completeFieldEdit': '请先完成当前字段的编辑',
  'providerDialog.confirmDeleteField': '确定要删除该字段吗？',
  'providerDialog.selectFieldsToDelete': '请先选择要删除的字段',
  'providerDialog.confirmBatchDeleteFields': '确定要删除选中的{count}个字段吗？',
  'providerDialog.batchDeleteFieldsSuccess': '成功删除{count}个字段',
};