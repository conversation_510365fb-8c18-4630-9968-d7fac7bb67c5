<template>
  <div class="welcome">
    <HeaderBar />

    <div class="operation-bar">
      <h2 class="page-title">长记忆管理</h2>
      <div class="right-operations">
        <el-button @click="goBack" type="primary">返回设备列表</el-button>
      </div>
    </div>

    <div class="main-wrapper">
      <!-- 设备信息 -->
      <el-card class="device-info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>设备信息</span>
        </div>
        <div class="device-info">
          <div class="info-item">
            <span class="label">设备ID:</span>
            <span class="value">{{ deviceId }}</span>
          </div>
          <div class="info-item">
            <span class="label">MAC地址:</span>
            <span class="value">{{ deviceInfo.macAddress || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">设备型号:</span>
            <span class="value">{{ deviceInfo.board || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">设备别名:</span>
            <span class="value">{{ deviceInfo.alias || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">固件版本:</span>
            <span class="value">{{ deviceInfo.appVersion || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">使用时长:</span>
            <span class="value">{{ deviceInfo.usageDuration || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后连接:</span>
            <span class="value">{{ deviceInfo.lastConnectedAt ? formatDate(deviceInfo.lastConnectedAt) : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">离线时长:</span>
            <span class="value">{{ deviceInfo.offlineDuration || '-' }}</span>
          </div>
        </div>
      </el-card>

      <div class="content-panel">
        <!-- 标签页 -->
        <el-card class="memory-card" shadow="never">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <!-- 用户画像 -->
            <el-tab-pane label="用户画像" name="profiles">
              <div class="tab-content">
                <div class="tab-header">
                  <el-button type="primary" @click="showAddProfileDialog">添加画像</el-button>
                  <el-button @click="refreshProfiles">刷新</el-button>
                </div>

                <div class="table-container">
                  <el-table :data="profiles" v-loading="profilesLoading" style="width: 100%">
                  <el-table-column prop="id" label="ID" width="200"></el-table-column>
                  <el-table-column prop="topic" label="主题" width="150"></el-table-column>
                  <el-table-column prop="subTopic" label="子主题" width="150"></el-table-column>
                  <el-table-column prop="content" label="内容" min-width="300"></el-table-column>
                  <el-table-column prop="createdAt" label="创建时间" width="180">
                    <template slot-scope="scope">
                      {{ formatDate(scope.row.createdAt) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                      <el-button size="mini" type="danger" @click="deleteProfile(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 用户事件 -->
            <el-tab-pane label="用户事件" name="events">
              <div class="tab-content">
                <div class="tab-header">
                  <div class="left-controls">
                    <el-button @click="refreshEvents">刷新</el-button>
                    <el-input-number v-model="eventLimit" :min="10" :max="200" label="显示数量"></el-input-number>
                  </div>
                  <div class="right-info">
                    <el-alert
                      title="用户事件是系统自动生成的历史记录，仅供查看，不支持编辑或删除"
                      type="info"
                      :closable="false"
                      show-icon>
                    </el-alert>
                  </div>
                </div>

                <div class="table-container">
                  <el-table :data="events" v-loading="eventsLoading" style="width: 100%" class="transparent-table">
                  <el-table-column prop="id" label="ID" width="200">
                    <template slot-scope="scope">
                      <el-tooltip :content="scope.row.id" placement="top">
                        <span class="truncated-id">{{ scope.row.id.substring(0, 8) }}...</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="content" label="内容" min-width="400">
                    <template slot-scope="scope">
                      <div class="event-content">
                        {{ scope.row.content }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="timestamp" label="时间戳" width="180">
                    <template slot-scope="scope">
                      {{ formatDate(scope.row.timestamp) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="similarity" label="相似度" width="100">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.similarity" :type="getSimilarityTagType(scope.row.similarity)">
                        {{ scope.row.similarity.toFixed(3) }}
                      </el-tag>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 长记忆上下文 -->
            <el-tab-pane label="长记忆上下文" name="context">
              <div class="tab-content">
                <div class="tab-header">
                  <el-button @click="refreshContext">刷新</el-button>
                  <el-input-number v-model="maxTokens" :min="500" :max="8000" label="最大Token数"></el-input-number>
                </div>
                
                <div class="context-content">
                  <el-input type="textarea" v-model="contextContent" readonly
                    placeholder="长记忆上下文将显示在这里...">
                  </el-input>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
    </div>

    <!-- 添加画像对话框 -->
    <el-dialog title="添加用户画像" :visible.sync="addProfileDialogVisible" width="500px">
      <el-form :model="newProfile" label-width="80px">
        <el-form-item label="主题">
          <el-input v-model="newProfile.topic" placeholder="请输入主题"></el-input>
        </el-form-item>
        <el-form-item label="子主题">
          <el-input v-model="newProfile.subTopic" placeholder="请输入子主题（可选）"></el-input>
        </el-form-item>
        <el-form-item label="内容">
          <el-input type="textarea" v-model="newProfile.content" :rows="4" placeholder="请输入画像内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addProfileDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addProfile" :loading="addingProfile">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import HeaderBar from '@/components/HeaderBar.vue';
import Api from '@/apis/api';

export default {
  name: 'MemoryManagement',
  components: {
    HeaderBar
  },
  data() {
    return {
      deviceId: '',
      deviceInfo: {},
      activeTab: 'profiles',
      
      // 用户画像
      profiles: [],
      profilesLoading: false,
      
      // 用户事件
      events: [],
      eventsLoading: false,
      eventLimit: 50,
      
      // 长记忆上下文
      contextContent: '',
      contextLoading: false,
      maxTokens: 2000,
      
      // 添加画像对话框
      addProfileDialogVisible: false,
      addingProfile: false,
      newProfile: {
        topic: '',
        subTopic: '',
        content: ''
      }
    }
  },
  mounted() {
    this.deviceId = this.$route.params.deviceId;
    this.loadDeviceInfo();
    this.loadProfiles();
  },
  methods: {
    // 返回设备列表
    goBack() {
      this.$router.push('/all-device-management');
    },

    // 加载设备信息
    loadDeviceInfo() {
      Api.device.getDeviceInfo(this.deviceId, ({ data }) => {
        if (data.code === 0) {
          this.deviceInfo = data.data || {};
        } else {
          this.$message.error('获取设备信息失败: ' + data.msg);
        }
      });
    },

    // 标签页切换
    handleTabClick(tab) {
      if (tab.name === 'profiles') {
        this.loadProfiles();
      } else if (tab.name === 'events') {
        this.loadEvents();
      } else if (tab.name === 'context') {
        this.loadContext();
      }
    },

    // 加载用户画像
    loadProfiles() {
      this.profilesLoading = true;
      Api.memory.getDeviceProfiles(this.deviceId, {}, ({ data }) => {
        this.profilesLoading = false;
        if (data.code === 0) {
          this.profiles = data.data || [];
        } else {
          this.$message.error(data.msg || '获取用户画像失败');
        }
      });
    },

    // 刷新画像
    refreshProfiles() {
      this.loadProfiles();
    },

    // 加载用户事件
    loadEvents() {
      this.eventsLoading = true;
      Api.memory.getDeviceEvents(this.deviceId, { limit: this.eventLimit }, ({ data }) => {
        this.eventsLoading = false;
        if (data.code === 0) {
          this.events = data.data || [];
        } else {
          this.$message.error(data.msg || '获取用户事件失败');
        }
      });
    },

    // 刷新事件
    refreshEvents() {
      this.loadEvents();
    },

    // 加载长记忆上下文
    loadContext() {
      this.contextLoading = true;
      Api.memory.getDeviceContext(this.deviceId, { maxTokens: this.maxTokens }, ({ data }) => {
        this.contextLoading = false;
        if (data.code === 0) {
          this.contextContent = data.data.context || '';
        } else {
          this.$message.error(data.msg || '获取长记忆上下文失败');
        }
      });
    },

    // 刷新上下文
    refreshContext() {
      this.loadContext();
    },

    // 显示添加画像对话框
    showAddProfileDialog() {
      this.newProfile = {
        topic: '',
        subTopic: '',
        content: ''
      };
      this.addProfileDialogVisible = true;
    },

    // 添加画像
    addProfile() {
      if (!this.newProfile.topic || !this.newProfile.content) {
        this.$message.error('主题和内容不能为空');
        return;
      }

      this.addingProfile = true;
      Api.memory.updateDeviceProfile(this.deviceId, this.newProfile, ({ data }) => {
        this.addingProfile = false;
        if (data.code === 0) {
          this.$message.success('添加画像成功');
          this.addProfileDialogVisible = false;
          this.loadProfiles();
        } else {
          this.$message.error(data.msg || '添加画像失败');
        }
      });
    },

    // 删除画像
    deleteProfile(profile) {
      this.$confirm('确定要删除这个画像吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Api.memory.deleteDeviceProfile(this.deviceId, profile.id, ({ data }) => {
          if (data.code === 0) {
            this.$message.success('删除成功');
            this.loadProfiles();
          } else {
            this.$message.error(data.msg || '删除失败');
          }
        });
      });
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-';
      const date = new Date(dateStr);
      return date.toLocaleString('zh-CN');
    },

    // 获取相似度标签类型
    getSimilarityTagType(similarity) {
      if (similarity >= 0.8) return 'success';
      if (similarity >= 0.6) return 'warning';
      return 'info';
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.welcome {
  min-width: 900px;
  min-height: 100vh;
  display: flex;
  position: relative;
  flex-direction: column;
  background-color: var(--ant-background-color-light);
  font-family: var(--ant-font-family);
}

.main-wrapper {
  margin: var(--ant-margin) var(--ant-margin-lg);
  border-radius: var(--ant-border-radius-lg);
  min-height: calc(100vh - 4vh);
  box-shadow: var(--ant-box-shadow);
  position: relative;
  background-color: var(--ant-background-color-container);
  border: 1px solid var(--ant-border-color-split);
  display: flex;
  flex-direction: column;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
}

.page-title {
  font-size: 24px;
  margin: 0;
}

.right-operations {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.content-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: var(--ant-border-radius-lg);
  background-color: transparent;
}

.device-info-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: var(--ant-box-shadow);
}

.card-header {
  font-weight: 600;
  font-size: 16px;
}

.device-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 10px 0;
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 200px;
}

.label {
  font-weight: 600;
  margin-right: 8px;
  color: var(--ant-text-color-secondary);
  white-space: nowrap;
  min-width: 80px;
}

.value {
  color: var(--ant-text-color);
  word-break: break-all;
}

.memory-card {
  border: none;
  box-shadow: var(--ant-box-shadow);

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.tab-content {
  padding: 20px 0;
}

.table-container {
  margin-top: 20px;
}

.context-content {
  margin-top: 20px;

  :deep(.el-textarea__inner) {
    min-height: 400px !important;
    resize: vertical;
  }
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: var(--ant-background-color-elevated);
  border-radius: var(--ant-border-radius);
  border: 1px solid var(--ant-border-color-split);
}

.left-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.right-info {
  flex: 1;
  margin-left: 20px;
}

.truncated-id {
  font-family: monospace;
  color: var(--ant-text-color-secondary);
  cursor: pointer;
}

.event-content {
  line-height: 1.5;
  word-break: break-word;
}

.context-content {
  margin-top: 20px;
}

:deep(.transparent-table) {
  background: white;
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-table__body-wrapper {
    flex: 1;
    overflow-y: auto;
    max-height: none !important;
  }

  .el-table__header-wrapper {
    flex-shrink: 0;
  }

  .el-table__header th {
    background: white !important;
    color: black;
  }

  &::before {
    display: none;
  }

  .el-table__body tr {
    background-color: white;

    td {
      border-top: 1px solid rgba(0, 0, 0, 0.04);
      border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    }
  }
}

:deep(.el-tabs) {
  width: 100%;
}

:deep(.el-tabs__content) {
  width: 100%;
}

:deep(.el-tab-pane) {
  width: 100%;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__active-bar) {
  background-color: var(--ant-primary-color);
}

:deep(.el-tabs__item.is-active) {
  color: var(--ant-primary-color);
}

:deep(.el-alert) {
  border: none;
  border-radius: var(--ant-border-radius);
}

:deep(.el-alert--info) {
  background-color: #f0f9ff;
  color: #0369a1;
}

:deep(.el-table .el-button--text) {
  color: var(--ant-primary-color) !important;
}

:deep(.el-table .el-button--text:hover) {
  color: var(--ant-primary-color-hover) !important;
}
</style>
