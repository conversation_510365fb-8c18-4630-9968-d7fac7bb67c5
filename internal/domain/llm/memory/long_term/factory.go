package long_term

import (
	"fmt"
	"strings"

	"xiaozhi-esp32-server-golang/internal/domain/llm/memory/long_term/providers"
	"xiaozhi-esp32-server-golang/logger"

	"github.com/spf13/viper"
)

// ProviderFactory 长记忆提供者工厂
type ProviderFactory struct{}

// NewProviderFactory 创建提供者工厂
func NewProviderFactory() *ProviderFactory {
	return &ProviderFactory{}
}

// CreateProvider 根据配置创建长记忆提供者
func (f *ProviderFactory) CreateProvider() (providers.LongTermMemoryProvider, error) {
	providerType := strings.ToLower(viper.GetString("memory.long_term.provider"))
	enabled := viper.GetBool("memory.long_term.enabled")

	if !enabled {
		logger.Log().Info("长记忆功能未启用，使用空提供者")
		return &providers.NullProvider{}, nil
	}

	logger.Log().Infof("正在创建长记忆提供者: %s", providerType)

	switch providerType {
	case "memobase":
		return providers.NewMemobaseProvider()
	case "null", "":
		return &providers.NullProvider{}, nil
	default:
		logger.Log().Errorf("不支持的长记忆提供者类型: %s", providerType)
		return &NullProvider{}, fmt.Errorf("不支持的长记忆提供者类型: %s", providerType)
	}
}

// GetAvailableProviders 获取可用的提供者列表
func (f *ProviderFactory) GetAvailableProviders() []string {
	return []string{
		"memobase",
		"null",
	}
}

// ValidateConfig 验证配置
func (f *ProviderFactory) ValidateConfig() error {
	if !viper.GetBool("memory.long_term.enabled") {
		return nil // 未启用不需要验证
	}

	providerType := strings.ToLower(viper.GetString("memory.long_term.provider"))
	available := f.GetAvailableProviders()

	for _, p := range available {
		if p == providerType {
			return f.validateProviderConfig(providerType)
		}
	}

	return fmt.Errorf("不支持的长记忆提供者类型: %s, 可用类型: %v", providerType, available)
}

// validateProviderConfig 验证特定提供者的配置
func (f *ProviderFactory) validateProviderConfig(providerType string) error {
	switch providerType {
	case "memobase":
		return f.validateMemobaseConfig()
	case "null":
		return nil
	default:
		return fmt.Errorf("未知的提供者类型: %s", providerType)
	}
}

// validateMemobaseConfig 验证Memobase配置
func (f *ProviderFactory) validateMemobaseConfig() error {
	projectURL := viper.GetString("memory.long_term.providers.memobase.project_url")
	if projectURL == "" {
		return fmt.Errorf("memory.long_term.providers.memobase.project_url不能为空")
	}

	apiKey := viper.GetString("memory.long_term.providers.memobase.api_key")
	if apiKey == "" {
		return fmt.Errorf("memory.long_term.providers.memobase.api_key不能为空")
	}

	// 验证URL格式
	if !strings.HasPrefix(projectURL, "http://") && !strings.HasPrefix(projectURL, "https://") {
		return fmt.Errorf("memory.long_term.providers.memobase.project_url必须是有效的HTTP(S) URL")
	}

	return nil
}
