//go:build sherpa_onnx
// +build sherpa_onnx

package sherpa_onnx

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/registry/tts"
	"xiaozhi-esp32-server-golang/internal/util"
	"xiaozhi-esp32-server-golang/internal/util/singleton"
	log "xiaozhi-esp32-server-golang/logger"

	sherpa "github.com/k2-fsa/sherpa-onnx-go/sherpa_onnx"
	"gopkg.in/hraban/opus.v2"
)

// StreamConfig 流式TTS优化配置
type StreamConfig struct {
	ChunkSize     int // 文本块大小（字符数）
	BufferSize    int // 通道缓冲区大小
	MaxConcurrent int // 最大并发发送goroutine数
	BitrateKbps   int // Opus编码比特率
}

// DefaultStreamConfig 默认流式配置
var DefaultStreamConfig = StreamConfig{
	ChunkSize:     50,  // 50字符一块，适合中文句子
	BufferSize:    100, // 100帧缓冲区
	MaxConcurrent: 5,   // 最多5个并发发送
	BitrateKbps:   64,  // 64kbps高质量音频
}

// SherpaOnnxTTSProvider Sherpa-ONNX TTS提供者
type SherpaOnnxTTSProvider struct {
	tts          *sherpa.OfflineTts
	SpeakerID    int
	Speed        int
	streamConfig StreamConfig
}

func init() {
	// 注册 Sherpa-Onnx TTS 提供者
	tts.Register([]string{constants.TtsTypeSherpaOnnx}, "Sherpa-Onnx TTS 语音合成服务", func(config map[string]interface{}) (tts.BaseTTSProvider, error) {
		provider := NewSherpaOnnxTTSProvider(config)
		return provider, nil
	})

	// 注册Sherpa-ONNX单例到单例管理器
	singleton.RegisterNativeLibrary(
		singleton.KeySherpaONNX,
		initSherpaONNX,
		cleanupSherpaONNX,
	)
}

// initSherpaONNX 初始化Sherpa-ONNX单例
func initSherpaONNX() (*sherpa.OfflineTts, error) {
	log.Infof("Sherpa-ONNX TTS模型初始化开始")

	// 如果没有手动配置模型，尝试自动检测
	var ttsConfig sherpa.OfflineTtsConfig
	basePath := "config/models/tts"
	exists, modelDir := subDirExistsWithKeyword(basePath, "kokoro")
	if !exists {
		return nil, fmt.Errorf("未找到Kokoro模型目录，基础路径: %s", basePath)
	}

	log.Infof("自动检测到Kokoro模型目录: %s", modelDir)

	// 验证关键文件是否存在
	modelFile := path.Join(modelDir, "model.onnx")
	voicesFile := path.Join(modelDir, "voices.bin")
	tokensFile := path.Join(modelDir, "tokens.txt")

	if _, err := os.Stat(modelFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("模型文件不存在: %s", modelFile)
	}
	if _, err := os.Stat(voicesFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("语音文件不存在: %s", voicesFile)
	}
	if _, err := os.Stat(tokensFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("词汇文件不存在: %s", tokensFile)
	}

	ttsConfig.Model.Kokoro = sherpa.OfflineTtsKokoroModelConfig{
		Model:       modelFile,
		Voices:      voicesFile,
		Tokens:      tokensFile,
		DataDir:     path.Join(modelDir, "espeak-ng-data"),
		DictDir:     path.Join(modelDir, "dict"),
		Lexicon:     strings.Join([]string{path.Join(modelDir, "lexicon-us-en.txt"), path.Join(modelDir, "lexicon-zh.txt")}, ","),
		LengthScale: 0,
	}

	log.Infof("模型配置: Model=%s, Voices=%s, Tokens=%s", modelFile, voicesFile, tokensFile)

	tts := sherpa.NewOfflineTts(&ttsConfig)
	if tts == nil {
		return nil, fmt.Errorf("Sherpa-ONNX TTS模型初始化失败")
	}

	log.Infof("Sherpa-ONNX TTS模型初始化成功")
	return tts, nil
}

// cleanupSherpaONNX 清理Sherpa-ONNX单例
func cleanupSherpaONNX(tts *sherpa.OfflineTts) error {
	if tts != nil {
		log.Infof("开始清理Sherpa-ONNX资源...")
		sherpa.DeleteOfflineTts(tts)
		log.Infof("Sherpa-ONNX C++资源已安全释放")
	}
	return nil
}

// getSherpaONNX 获取Sherpa-ONNX单例实例
func getSherpaONNX() (*sherpa.OfflineTts, error) {
	return singleton.GetOrCreate(singleton.KeySherpaONNX, initSherpaONNX)
}

// NewSherpaOnnxTTSProvider 创建新的Sherpa-ONNX TTS提供者
func NewSherpaOnnxTTSProvider(config map[string]interface{}) *SherpaOnnxTTSProvider {
	// 使用配置助手简化配置获取
	helper := util.NewConfigHelper(config)

	// 获取或创建Sherpa-ONNX单例
	tts, err := getSherpaONNX()
	if err != nil {
		log.Errorf("获取Sherpa-ONNX实例失败: %v", err)
		return nil
	}

	provider := &SherpaOnnxTTSProvider{
		tts:          tts,
		SpeakerID:    helper.GetInt("speaker_id", 48),
		Speed:        helper.GetInt("speed", 1),
		streamConfig: DefaultStreamConfig, // 使用默认流式配置
	}

	// 如果配置中有流式相关设置，则覆盖默认值
	if streamConfig, ok := config["stream_config"]; ok {
		if streamMap, ok := streamConfig.(map[string]interface{}); ok {
			streamHelper := util.NewConfigHelper(streamMap)
			provider.streamConfig.ChunkSize = streamHelper.GetInt("chunk_size", DefaultStreamConfig.ChunkSize)
			provider.streamConfig.BufferSize = streamHelper.GetInt("buffer_size", DefaultStreamConfig.BufferSize)
			provider.streamConfig.MaxConcurrent = streamHelper.GetInt("max_concurrent", DefaultStreamConfig.MaxConcurrent)
			provider.streamConfig.BitrateKbps = streamHelper.GetInt("bitrate_kbps", DefaultStreamConfig.BitrateKbps)
		}
	}

	log.Infof("Sherpa-ONNX TTS Provider已创建，流式配置: chunk_size=%d, buffer_size=%d, max_concurrent=%d, bitrate=%dkbps",
		provider.streamConfig.ChunkSize, provider.streamConfig.BufferSize,
		provider.streamConfig.MaxConcurrent, provider.streamConfig.BitrateKbps)

	return provider
}

// TextToSpeech 将文本转换为语音，返回音频帧数据和错误
func (p *SherpaOnnxTTSProvider) TextToSpeech(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) ([][]byte, error) {
	if p.tts == nil {
		return nil, fmt.Errorf("Sherpa-ONNX TTS模型未初始化")
	}

	if text == "" {
		return nil, fmt.Errorf("输入文本不能为空")
	}

	log.Infof("Sherpa-ONNX TTS开始生成语音，文本: %s", text)

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 使用Sherpa-ONNX生成音频
	audio := p.tts.Generate(text, p.SpeakerID, float32(p.Speed))
	if audio == nil {
		return nil, fmt.Errorf("Sherpa-ONNX生成音频失败")
	}

	// 验证生成的音频数据
	if len(audio.Samples) == 0 {
		return nil, fmt.Errorf("Sherpa-ONNX生成的音频数据为空")
	}

	if audio.SampleRate <= 0 {
		return nil, fmt.Errorf("Sherpa-ONNX生成的音频采样率无效: %d", audio.SampleRate)
	}

	log.Infof("Sherpa-ONNX生成音频: 样本数=%d, 采样率=%d, 时长=%.2f秒",
		len(audio.Samples), audio.SampleRate, float64(len(audio.Samples))/float64(audio.SampleRate))

	// 直接将float32 PCM数据转换为Opus帧
	opusFrames, err := util.Float32PCMToOpus(audio.Samples, audio.SampleRate, sampleRate, channels, frameDuration)
	if err != nil {
		return nil, fmt.Errorf("Sherpa-ONNX PCM到Opus转换失败: %v", err)
	}

	if len(opusFrames) == 0 {
		return nil, fmt.Errorf("转换后的Opus帧数量为0")
	}

	log.Infof("Sherpa-ONNX TTS转换完成，生成 %d 个Opus帧", len(opusFrames))
	return opusFrames, nil
}

// TextToSpeechStream 优化的流式文本转语音实现
func (p *SherpaOnnxTTSProvider) TextToSpeechStream(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) (outputChan chan []byte, err error) {
	// 使用配置的缓冲区大小
	outputChan = make(chan []byte, p.streamConfig.BufferSize)

	go func() {
		defer func() {
			// 确保所有goroutine完成后再关闭channel
			log.Infof("流式TTS主处理goroutine即将退出，准备关闭输出通道")
			close(outputChan)
		}()

		// 开始性能监控
		startTime := time.Now()
		log.Infof("开始流式TTS处理，文本长度: %d 字符", len(text))

		// 文本分块处理，减少首次响应延迟
		chunks := p.splitTextIntoChunks(text, p.streamConfig.ChunkSize)
		totalChunks := len(chunks)

		log.Infof("文本已分为 %d 块进行处理", totalChunks)

		// 使用WaitGroup确保所有发送goroutine完成
		var wg sync.WaitGroup

		// 全局发送统计
		var totalGenerated, totalSent int64
		var sendStats sync.Mutex

		for i, chunk := range chunks {
			select {
			case <-ctx.Done():
				log.Infof("流式TTS被取消，已处理 %d/%d 块", i, totalChunks)
				// 等待已启动的goroutines完成
				wg.Wait()

				// 输出取消时的统计
				sendStats.Lock()
				cancelGenerated := totalGenerated
				cancelSent := totalSent
				sendStats.Unlock()

				if cancelGenerated > 0 {
					cancelSuccessRate := float64(cancelSent) / float64(cancelGenerated) * 100
					log.Infof("流式TTS取消统计 - 生成帧数: %d, 发送帧数: %d, 成功率: %.1f%%",
						cancelGenerated, cancelSent, cancelSuccessRate)
				}
				return
			default:
			}

			chunkStartTime := time.Now()

			// 处理单个文本块
			frames, err := p.processTextChunk(ctx, chunk, sampleRate, channels, frameDuration)
			if err != nil {
				log.Errorf("处理文本块 %d/%d 失败: %v", i+1, totalChunks, err)
				continue
			}

			chunkProcessTime := time.Since(chunkStartTime)
			log.Infof("文本块 %d/%d 处理完成，耗时: %v, 生成帧数: %d",
				i+1, totalChunks, chunkProcessTime, len(frames))

			// 更新生成统计
			sendStats.Lock()
			totalGenerated += int64(len(frames))
			sendStats.Unlock()

			// 启动发送goroutine前增加计数
			wg.Add(1)
			go p.sendFramesAsyncWithStats(ctx, outputChan, frames, i+1, totalChunks, &wg, &totalSent, &sendStats)
		}

		// 等待所有发送goroutines完成
		log.Infof("所有文本块处理完成，等待发送goroutines完成...")
		wg.Wait()

		// 最终统计报告
		sendStats.Lock()
		finalGenerated := totalGenerated
		finalSent := totalSent
		sendStats.Unlock()

		totalTime := time.Since(startTime)
		successRate := float64(0)
		if finalGenerated > 0 {
			successRate = float64(finalSent) / float64(finalGenerated) * 100
		}

		log.Infof("流式TTS处理完成 - 总耗时: %v, 平均每块: %v",
			totalTime, totalTime/time.Duration(totalChunks))
		log.Infof("发送统计 - 生成帧数: %d, 成功发送: %d, 成功率: %.1f%%",
			finalGenerated, finalSent, successRate)

		if successRate < 90 {
			log.Warnf("发送成功率较低(%.1f%%)，可能存在消费者处理过慢或提前退出的情况", successRate)
		}
	}()

	return outputChan, nil
}

// splitTextIntoChunks 将长文本分割成小块，按句子边界分割
func (p *SherpaOnnxTTSProvider) splitTextIntoChunks(text string, maxChunkSize int) []string {
	if len(text) <= maxChunkSize {
		return []string{text}
	}

	var chunks []string
	sentences := strings.FieldsFunc(text, func(r rune) bool {
		return r == '。' || r == '！' || r == '？' || r == '.' || r == '!' || r == '?'
	})

	currentChunk := ""
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if sentence == "" {
			continue
		}

		// 如果当前块加上新句子超过限制，先保存当前块
		if len(currentChunk)+len(sentence) > maxChunkSize && currentChunk != "" {
			chunks = append(chunks, currentChunk)
			currentChunk = sentence
		} else {
			if currentChunk != "" {
				currentChunk += "。" + sentence
			} else {
				currentChunk = sentence
			}
		}
	}

	// 添加最后一块
	if currentChunk != "" {
		chunks = append(chunks, currentChunk)
	}

	return chunks
}

// processTextChunk 处理单个文本块，优化转换链路
func (p *SherpaOnnxTTSProvider) processTextChunk(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) ([][]byte, error) {
	if p.tts == nil {
		return nil, fmt.Errorf("Sherpa-ONNX TTS模型未初始化")
	}

	log.Infof("SherpaOnnxTTSProvider开始处理文本块: %s", text)

	// 开始音频生成
	generatedAudio := p.tts.Generate(text, p.SpeakerID, float32(p.Speed))
	if generatedAudio == nil {
		return nil, fmt.Errorf("Sherpa-ONNX生成音频失败，返回nil")
	}

	if len(generatedAudio.Samples) == 0 {
		log.Warnf("文本块 '%s' 生成的音频样本为空", text)
		return [][]byte{}, nil
	}

	if generatedAudio.SampleRate <= 0 {
		return nil, fmt.Errorf("Sherpa-ONNX生成的音频采样率无效: %d", generatedAudio.SampleRate)
	}

	// 优化转换：直接使用快速PCM到Opus转换
	opusFrames, err := p.fastFloat32ToOpus(generatedAudio.Samples, generatedAudio.SampleRate, sampleRate, channels, frameDuration)
	if err != nil {
		return nil, fmt.Errorf("快速PCM到Opus转换失败: %v", err)
	}

	return opusFrames, nil
}

// fastFloat32ToOpus 快速Float32到Opus转换，跳过WAV中间格式
func (p *SherpaOnnxTTSProvider) fastFloat32ToOpus(samples []float32, inputSampleRate, outputSampleRate, channels, frameDuration int) ([][]byte, error) {
	// 如果采样率不匹配，进行重采样
	if inputSampleRate != outputSampleRate {
		samples = util.ResampleLinearFloat32(samples, inputSampleRate, outputSampleRate)
	}

	// 将Float32直接转换为int16（跳过WAV封装）
	int16Samples := make([]int16, len(samples))
	for i, sample := range samples {
		// 限制范围到int16
		if sample > 1.0 {
			sample = 1.0
		} else if sample < -1.0 {
			sample = -1.0
		}
		int16Samples[i] = int16(sample * 32767)
	}

	// 使用优化的分块Opus编码
	return p.encodeOpusInChunks(int16Samples, outputSampleRate, channels, frameDuration)
}

// encodeOpusInChunks 分块编码Opus，减少内存使用和延迟
func (p *SherpaOnnxTTSProvider) encodeOpusInChunks(samples []int16, sampleRate, channels, frameDuration int) ([][]byte, error) {
	// 计算每帧样本数
	samplesPerFrame := sampleRate * frameDuration / 1000 * channels
	if samplesPerFrame <= 0 {
		return nil, fmt.Errorf("无效的帧大小配置")
	}

	// 创建Opus编码器
	encoder, err := opus.NewEncoder(sampleRate, channels, opus.AppVoIP)
	if err != nil {
		return nil, fmt.Errorf("创建Opus编码器失败: %v", err)
	}

	// 设置比特率 - 使用配置值
	encoder.SetBitrate(p.streamConfig.BitrateKbps * 1000) // 转换为bps

	var opusFrames [][]byte
	for i := 0; i < len(samples); i += samplesPerFrame {
		end := i + samplesPerFrame
		var frameData []int16

		if end > len(samples) {
			// 对于最后一帧，如果不足完整帧，用零填充
			frameData = make([]int16, samplesPerFrame)
			copy(frameData, samples[i:])
		} else {
			frameData = samples[i:end]
		}

		// 编码为Opus
		frame := make([]byte, 1000) // 预分配缓冲区
		n, err := encoder.Encode(frameData, frame)
		if err != nil {
			log.Warnf("Opus编码帧失败: %v", err)
			continue
		}

		if n > 0 {
			opusFrames = append(opusFrames, frame[:n])
		}
	}

	return opusFrames, nil
}

// sendFramesAsyncWithStats 异步发送音频帧并更新统计，避免阻塞主处理流程
func (p *SherpaOnnxTTSProvider) sendFramesAsyncWithStats(ctx context.Context, outputChan chan []byte, frames [][]byte, chunkIndex, totalChunks int, wg *sync.WaitGroup, totalSent *int64, statsMutex *sync.Mutex) {
	defer func() {
		// 完成WaitGroup计数
		wg.Done()

		// panic恢复机制
		if r := recover(); r != nil {
			log.Errorf("块 %d/%d 发送过程中发生panic: %v", chunkIndex, totalChunks, r)
		}
	}()

	framesSent := 0
	failureCount := 0
	sendStartTime := time.Now()

	for i, frame := range frames {
		select {
		case <-ctx.Done():
			log.Infof("块 %d/%d 发送被取消，已发送 %d/%d 帧", chunkIndex, totalChunks, framesSent, len(frames))
			// 更新全局统计
			statsMutex.Lock()
			*totalSent += int64(framesSent)
			statsMutex.Unlock()
			return
		default:
			// 使用非阻塞发送，防止channel关闭导致panic
			if p.safeSendFrame(outputChan, frame) {
				framesSent++
				failureCount = 0 // 重置失败计数
			} else {
				failureCount++
				log.Warnf("块 %d/%d 第 %d 帧发送失败，连续失败次数: %d，输出通道可能已关闭或消费者处理过慢",
					chunkIndex, totalChunks, i+1, failureCount)

				// 如果连续失败超过阈值，认为消费者已退出，停止发送
				if failureCount >= 3 {
					log.Warnf("块 %d/%d 连续发送失败达到阈值，停止发送剩余 %d 帧",
						chunkIndex, totalChunks, len(frames)-i)
					break
				}

				// 轻微延迟，给消费者一些处理时间
				time.Sleep(5 * time.Millisecond)
			}
		}
	}

	// 更新全局统计
	statsMutex.Lock()
	*totalSent += int64(framesSent)
	statsMutex.Unlock()

	sendDuration := time.Since(sendStartTime)
	if framesSent > 0 {
		avgSendTime := sendDuration / time.Duration(framesSent)
		log.Infof("块 %d/%d 发送统计：成功 %d/%d 帧，耗时 %v，平均每帧 %v",
			chunkIndex, totalChunks, framesSent, len(frames), sendDuration, avgSendTime)
	} else {
		log.Warnf("块 %d/%d 发送失败：0/%d 帧发送成功", chunkIndex, totalChunks, len(frames))
	}
}

// sendFramesAsync 异步发送音频帧，避免阻塞主处理流程（保留向后兼容）
func (p *SherpaOnnxTTSProvider) sendFramesAsync(ctx context.Context, outputChan chan []byte, frames [][]byte, chunkIndex, totalChunks int, wg *sync.WaitGroup) {
	// 委托给带统计的版本，使用空的统计参数
	var dummyTotal int64
	var dummyMutex sync.Mutex
	p.sendFramesAsyncWithStats(ctx, outputChan, frames, chunkIndex, totalChunks, wg, &dummyTotal, &dummyMutex)
}

// safeSendFrame 安全发送帧数据，防止向已关闭的channel发送
func (p *SherpaOnnxTTSProvider) safeSendFrame(outputChan chan []byte, frame []byte) bool {
	defer func() {
		if r := recover(); r != nil {
			log.Debugf("向已关闭的channel发送数据被捕获: %v", r)
		}
	}()

	select {
	case outputChan <- frame:
		return true
	default:
		// channel满了或已关闭，检查channel状态
		select {
		case outputChan <- frame:
			return true
		case <-time.After(10 * time.Millisecond):
			// 轻微延迟后再次尝试，如果仍然失败则认为channel已关闭或消费者处理太慢
			return false
		}
	}
}

// GetVoiceInfo 获取语音信息
func (p *SherpaOnnxTTSProvider) GetVoiceInfo() map[string]interface{} {
	return map[string]interface{}{
		"type": "sherpa_onnx",
	}
}

// Close 关闭TTS提供者并释放资源
func (p *SherpaOnnxTTSProvider) Close() error {
	if p.tts != nil {

		p.tts = nil

	}

	return nil
}

func subDirExistsWithKeyword(rootDir, keyword string) (bool, string) {
	// 读取目录下的所有条目
	entries, err := os.ReadDir(rootDir)
	if err != nil {
		// 如果目录不存在或无法读取，返回错误
		return false, ""
	}

	// 遍历所有条目
	for _, entry := range entries {
		// 检查当前条目是否是目录
		if entry.IsDir() {
			// 如果是目录，检查其名称是否包含关键字
			if strings.Contains(entry.Name(), keyword) {
				// 如果找到，立即返回 true
				return true, filepath.Join(rootDir, entry.Name())
			}
		}
	}

	// 如果遍历完所有子目录都没有找到，返回 false
	return false, ""
}
