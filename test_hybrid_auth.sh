#!/bin/bash

# 混合认证功能测试脚本

BASE_URL="http://localhost:8003/xiaozhi"
SYSTEM_SECRET="cc36d3ca-b065-47ef-ae29-d34f7540abd1"
USER_TOKEN="your_user_token_here"  # 这里需要一个真实的用户token
DEVICE_ID="42:32:13:b2:7b:f2"

echo "=== 混合认证功能测试 ==="
echo

# 1. 测试系统密钥访问设备信息
echo "1. 测试系统密钥访问设备信息"
echo "请求: GET $BASE_URL/device/info/$DEVICE_ID"
echo "认证: 系统密钥"
response=$(curl -s -X GET "$BASE_URL/device/info/$DEVICE_ID" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json")
echo "响应: $response"
echo

# 2. 测试系统密钥上报设备状态
echo "2. 测试系统密钥上报设备状态"
echo "请求: POST $BASE_URL/device/report-status"
echo "认证: 系统密钥"
response=$(curl -s -X POST "$BASE_URL/device/report-status" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "'$DEVICE_ID'",
    "sessionDuration": 120,
    "isOnline": true,
    "appVersion": "1.0.0"
  }')
echo "响应: $response"
echo

# 3. 测试系统密钥获取用户设备列表（应该返回所有设备）
echo "3. 测试系统密钥获取设备列表（应该返回所有设备）"
echo "请求: GET $BASE_URL/device/bind/test-agent"
echo "认证: 系统密钥"
response=$(curl -s -X GET "$BASE_URL/device/bind/test-agent" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json")
echo "响应: $response"
echo

# 4. 测试无效token
echo "4. 测试无效token"
echo "请求: GET $BASE_URL/device/info/$DEVICE_ID"
echo "认证: 无效token"
response=$(curl -s -X GET "$BASE_URL/device/info/$DEVICE_ID" \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json")
echo "响应: $response"
echo

# 5. 测试无认证
echo "5. 测试无认证"
echo "请求: GET $BASE_URL/device/info/$DEVICE_ID"
echo "认证: 无"
response=$(curl -s -X GET "$BASE_URL/device/info/$DEVICE_ID" \
  -H "Content-Type: application/json")
echo "响应: $response"
echo

# 6. 验证设备使用时长是否正确累计
echo "6. 验证设备使用时长累计"
echo "第一次上报..."
curl -s -X POST "$BASE_URL/device/report-status" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "'$DEVICE_ID'",
    "sessionDuration": 60,
    "isOnline": true,
    "appVersion": "1.0.0"
  }' > /dev/null

echo "第二次上报..."
curl -s -X POST "$BASE_URL/device/report-status" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "'$DEVICE_ID'",
    "sessionDuration": 30,
    "isOnline": true,
    "appVersion": "1.0.0"
  }' > /dev/null

echo "查询累计使用时长..."
response=$(curl -s -X GET "$BASE_URL/device/info/$DEVICE_ID" \
  -H "Authorization: Bearer $SYSTEM_SECRET" \
  -H "Content-Type: application/json")
echo "响应: $response"
echo

echo "=== 测试完成 ==="
echo
echo "说明："
echo "- 系统密钥: $SYSTEM_SECRET"
echo "- 系统密钥可以访问所有API和所有设备"
echo "- 普通用户token只能访问自己的设备"
echo "- 无效token或无认证会返回401错误"
echo "- 设备使用时长会正确累计"
