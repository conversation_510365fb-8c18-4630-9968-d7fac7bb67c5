package server

import (
	"context"
	"time"
	"xiaozhi-esp32-server-golang/internal/app/mqtt_server"
	"xiaozhi-esp32-server-golang/internal/app/server/chat"
	"xiaozhi-esp32-server-golang/internal/app/server/mqtt_udp"
	"xiaozhi-esp32-server-golang/internal/app/server/types"
	"xiaozhi-esp32-server-golang/internal/app/server/websocket"
	"xiaozhi-esp32-server-golang/internal/global"
	"xiaozhi-esp32-server-golang/internal/manager_api"
	"xiaozhi-esp32-server-golang/internal/util/cleanup"
	log "xiaozhi-esp32-server-golang/logger"

	"github.com/spf13/viper"
)

// App 统一管理所有协议服务和 ChatManager

type App struct {
	wsServer             *websocket.WebSocketServer
	mqttUdpAdapter       *mqtt_udp.MqttUdpAdapter
	managerAPIService    manager_api.ManagerAPIService
	deviceStatusManager  *manager_api.DeviceStatusManager
}

func NewApp() *App {
	var err error
	app := &App{}
	app.wsServer = app.newWebSocketServer()
	app.mqttUdpAdapter, err = app.newMqttUdpAdapter()
	if err != nil {
		log.Errorf("newMqttUdpAdapter err: %+v", err)
		return nil
	}

	// 初始化manager-api服务
	app.managerAPIService, err = app.newManagerAPIService()
	if err != nil {
		log.Errorf("newManagerAPIService err: %+v", err)
		// manager-api服务初始化失败不影响主服务启动
	}

	// 初始化设备状态管理器
	if app.managerAPIService != nil {
		if service, ok := app.managerAPIService.(*manager_api.Service); ok {
			if httpClient := service.GetClient(); httpClient != nil {
				app.deviceStatusManager = manager_api.NewDeviceStatusManager(httpClient)
				global.SetGlobalDeviceStatusManager(app.deviceStatusManager)
				log.Info("设备状态管理器初始化成功")
			}
		}
	}

	return app
}

func (a *App) Run() {
	// 启动manager-api服务
	if a.managerAPIService != nil {
		ctx := context.Background()
		go func() {
			if err := a.managerAPIService.Start(ctx); err != nil {
				log.Errorf("manager-api service start failed: %v", err)
			} else {
				log.Info("manager-api service started successfully")
			}
		}()
	}

	go a.wsServer.Start()
	if viper.GetBool("mqtt_server.enable") {
		go func() {
			err := a.startMqttServer()
			if err != nil {
				log.Errorf("startMqttServer err: %+v", err)
			}
		}()
	}
	if a.mqttUdpAdapter != nil {
		time.Sleep(1 * time.Second)
		go a.mqttUdpAdapter.Start()
	}

	// 注册聊天相关的本地MCP工具
	a.registerChatMCPTools()

	select {} // 阻塞主线程
}

// Close 关闭应用程序并清理资源
func (a *App) Close() {
	log.Info("正在关闭应用程序...")

	// 关闭设备状态管理器
	if a.deviceStatusManager != nil {
		a.deviceStatusManager.Close()
	}

	// 关闭manager-api服务
	if a.managerAPIService != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		if err := a.managerAPIService.Stop(ctx); err != nil {
			log.Errorf("关闭manager-api服务失败: %v", err)
		}
	}

	log.Info("应用程序已关闭")
}

func (app *App) newMqttUdpAdapter() (*mqtt_udp.MqttUdpAdapter, error) {
	isEnableUdp := viper.GetBool("mqtt.enable")
	if !isEnableUdp {
		return nil, nil
	}
	mqttConfig := mqtt_udp.MqttConfig{
		Broker:   viper.GetString("mqtt.broker"),
		Type:     viper.GetString("mqtt.type"),
		Port:     viper.GetInt("mqtt.port"),
		ClientID: viper.GetString("mqtt.client_id"),
		Username: viper.GetString("mqtt.username"),
		Password: viper.GetString("mqtt.password"),
	}

	udpServer, err := app.newUdpServer()
	if err != nil {
		return nil, err
	}

	return mqtt_udp.NewMqttUdpAdapter(
		&mqttConfig,
		mqtt_udp.WithUdpServer(udpServer),
		mqtt_udp.WithOnNewConnection(app.OnNewConnection),
	), nil
}

func (app *App) newUdpServer() (*mqtt_udp.UdpServer, error) {
	udpPort := viper.GetInt("udp.listen_port")
	externalHost := viper.GetString("udp.external_host")
	externalPort := viper.GetInt("udp.external_port")

	udpServer := mqtt_udp.NewUDPServer(udpPort, externalHost, externalPort)
	err := udpServer.Start()
	if err != nil {
		log.Fatalf("udpServer.Start err: %+v", err)
		return nil, err
	}
	return udpServer, nil
}

func (app *App) newWebSocketServer() *websocket.WebSocketServer {
	port := viper.GetInt("websocket.port")
	return websocket.NewWebSocketServer(port, websocket.WithOnNewConnection(app.OnNewConnection))
}

func (app *App) startMqttServer() error {
	return mqtt_server.StartMqttServer()
}

// 所有协议新连接都走这里
func (a *App) OnNewConnection(transport types.IConn) {
	deviceID := transport.GetDeviceID()

	//need delete
	chatManager, err := chat.NewChatManager(deviceID, transport, a.GetManagerAPIService())
	if err != nil {
		log.Errorf("创建chatManager失败: %v", err)
		return
	}

	go chatManager.Start()
}

// registerChatMCPTools 注册聊天相关的本地MCP工具
func (s *App) registerChatMCPTools() {
	// 调用chat包的注册函数
	chat.RegisterChatMCPTools()

	log.Info("聊天相关的本地MCP工具注册完成")
}

// newManagerAPIService 创建manager-api服务
func (a *App) newManagerAPIService() (manager_api.ManagerAPIService, error) {
	// 读取manager-api配置
	config := &manager_api.Config{
		Enabled:                    viper.GetBool("manager_api.enabled"),
		BaseURL:                    viper.GetString("manager_api.base_url"),
		Secret:                     viper.GetString("manager_api.secret"),
		TimeoutSeconds:             viper.GetInt("manager_api.timeout_seconds"),
		RetryAttempts:              viper.GetInt("manager_api.retry_attempts"),
		RetryDelayMs:               viper.GetInt("manager_api.retry_delay_ms"),
		MaxRetryDelayMs:            viper.GetInt("manager_api.max_retry_delay_ms"),
		CacheTTLSeconds:            viper.GetInt("manager_api.cache_ttl_seconds"),
		HealthCheckIntervalSeconds: viper.GetInt("manager_api.health_check_interval_seconds"),
		BatchSize:                  viper.GetInt("manager_api.batch_size"),
		FlushIntervalSeconds:       viper.GetInt("manager_api.flush_interval_seconds"),
	}

	// 设置默认值
	if config.TimeoutSeconds == 0 {
		config.TimeoutSeconds = 30
	}
	if config.RetryAttempts == 0 {
		config.RetryAttempts = 3
	}
	if config.RetryDelayMs == 0 {
		config.RetryDelayMs = 1000
	}
	if config.MaxRetryDelayMs == 0 {
		config.MaxRetryDelayMs = 10000
	}
	if config.CacheTTLSeconds == 0 {
		config.CacheTTLSeconds = 300
	}
	if config.HealthCheckIntervalSeconds == 0 {
		config.HealthCheckIntervalSeconds = 30
	}
	if config.BatchSize == 0 {
		config.BatchSize = 50
	}
	if config.FlushIntervalSeconds == 0 {
		config.FlushIntervalSeconds = 10
	}

	config.HealthCheckInterval = time.Duration(config.HealthCheckIntervalSeconds) * time.Second

	if !config.Enabled {
		log.Info("manager-api service is disabled")
		return nil, nil
	}

	service, err := manager_api.NewService(config)
	if err != nil {
		return nil, err
	}

	log.Info("manager-api service initialized successfully")
	return service, nil
}

// GetManagerAPIService 获取manager-api服务实例
func (a *App) GetManagerAPIService() manager_api.ManagerAPIService {
	return a.managerAPIService
}

// Shutdown 优雅关闭应用
func (a *App) Shutdown(ctx context.Context) error {
	log.Info("开始关闭应用服务...")

	// 关闭manager-api服务
	if a.managerAPIService != nil {
		log.Info("关闭manager-api服务...")
		if err := a.managerAPIService.Stop(ctx); err != nil {
			log.Errorf("关闭manager-api服务失败: %v", err)
		} else {
			log.Info("manager-api服务已关闭")
		}
	}

	// TODO: 添加其他服务的关闭逻辑
	// if a.wsServer != nil {
	//     a.wsServer.Stop()
	// }
	// if a.mqttUdpAdapter != nil {
	//     a.mqttUdpAdapter.Stop()
	// }

	// 执行全局资源清理 - 通用的清理机制
	log.Info("执行全局资源清理...")
	if err := cleanup.Cleanup(); err != nil {
		log.Errorf("全局资源清理过程中出现错误: %v", err)
		// 不返回错误，继续完成其他清理工作
	} else {
		log.Info("全局资源清理完成")
	}

	log.Info("应用服务关闭完成")
	return nil
}
