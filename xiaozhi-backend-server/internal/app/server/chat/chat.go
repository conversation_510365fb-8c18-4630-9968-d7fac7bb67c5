package chat

import (
	"context"
	"encoding/json"
	"sync"

	"github.com/spf13/viper"

	"xiaozhi-esp32-server-golang/constants"
	types_conn "xiaozhi-esp32-server-golang/internal/app/server/types"
	types_audio "xiaozhi-esp32-server-golang/internal/data/audio"
	. "xiaozhi-esp32-server-golang/internal/data/client"
	userconfig "xiaozhi-esp32-server-golang/internal/domain/config"
	utypes "xiaozhi-esp32-server-golang/internal/domain/config/types"
	llm_memory "xiaozhi-esp32-server-golang/internal/domain/llm/memory"
	"xiaozhi-esp32-server-golang/internal/domain/llm/memory/hybrid"
	"xiaozhi-esp32-server-golang/internal/domain/vad/silero_vad"
	"xiaozhi-esp32-server-golang/internal/global"
	"xiaozhi-esp32-server-golang/internal/manager_api"
	manager_api_types "xiaozhi-esp32-server-golang/internal/manager_api/types"
	log "xiaozhi-esp32-server-golang/logger"
)

type ChatManager struct {
	DeviceID  string
	transport types_conn.IConn

	clientState *ClientState
	session     *ChatSession
	ctx         context.Context
	cancel      context.CancelFunc
}

type ChatManagerOption func(*ChatManager)

func NewChatManager(deviceID string, transport types_conn.IConn, managerAPI manager_api.ManagerAPIService, options ...ChatManagerOption) (*ChatManager, error) {
	cm := &ChatManager{
		DeviceID:  deviceID,
		transport: transport,
	}

	for _, option := range options {
		option(cm)
	}

	ctx := context.WithValue(context.Background(), "chat_session_operator", ChatSessionOperator(cm))

	cm.ctx, cm.cancel = context.WithCancel(ctx)

	cm.transport.OnClose(cm.OnClose)

	clientState, err := GenClientState(managerAPI, cm.ctx, cm.DeviceID)
	if err != nil {
		log.Errorf("初始化客户端状态失败: %v", err)
		return nil, err
	}
	cm.clientState = clientState

	serverTransport := NewServerTransport(cm.transport, clientState)

	cm.session = NewChatSession(
		clientState,
		serverTransport,
	)

	return cm, nil
}

func GenClientState(managerAPI manager_api.ManagerAPIService, pctx context.Context, deviceID string) (*ClientState, error) {
	configProvider, err := userconfig.GetProvider()
	if err != nil {
		log.Errorf("获取 用户配置提供者失败: %+v", err)
		return nil, err
	}

	// 🔄 首先尝试从 manager-api 获取设备特定的AI模型配置
	deviceConfig, err := getDeviceConfigWithManagerAPI(pctx, managerAPI, deviceID, configProvider)
	if err != nil {
		log.Errorf("获取 设备 %s 配置失败: %+v", deviceID, err)
		return nil, err
	}

	if deviceConfig.Vad.Provider == "silero_vad" {
		silero_vad.InitVadPool(deviceConfig.Vad.Config)
	}

	// 创建带取消功能的上下文
	ctx, cancel := context.WithCancel(pctx)

	maxSilenceDuration := viper.GetInt64("chat.chat_max_silence_duration")
	if maxSilenceDuration == 0 {
		maxSilenceDuration = 200
	}

	clientState := &ClientState{
		Dialogue:     &Dialogue{},
		Abort:        false,
		ListenMode:   "auto",
		DeviceID:     deviceID,
		Ctx:          ctx,
		Cancel:       cancel,
		SystemPrompt: deviceConfig.SystemPrompt,
		DeviceConfig: deviceConfig,
		OutputAudioFormat: types_audio.AudioFormat{
			SampleRate:    types_audio.SampleRate,
			Channels:      types_audio.Channels,
			FrameDuration: types_audio.FrameDuration,
			Format:        types_audio.Format,
		},
		OpusAudioBuffer: make(chan []byte, 100),
		AsrAudioBuffer: &AsrAudioBuffer{
			PcmData:          make([]float32, 0),
			AudioBufferMutex: sync.RWMutex{},
			PcmFrameSize:     0,
		},
		VoiceStatus: VoiceStatus{
			HaveVoice:            false,
			HaveVoiceLastTime:    0,
			VoiceStop:            false,
			SilenceThresholdTime: maxSilenceDuration,
		},
		SessionCtx: Ctx{},
	}
	localMemory := llm_memory.Get(&deviceConfig.Memory)
	if localMemory != nil {
		// 使用全局记忆配置
		historyMessages, err := localMemory.GetMessages(ctx, deviceID, 15)
		if err != nil {
			log.Errorf("从全局记忆获取对话历史失败: %v", err)
		} else {
			clientState.InitMessages(historyMessages)
			log.Infof("为设备 %s 从全局记忆加载了 %d 条历史消息", deviceID, len(historyMessages))
		}

		// 预获取用户画像验证连接性
		userProfile, err := localMemory.GetUserProfile(ctx, deviceID)
		if err != nil {
			log.Debugf("在会话初始化时获取用户画像失败: %v", err)
		} else if userProfile != "" {
			log.Infof("为设备 %s 在会话初始化时获取到用户画像（全局配置），长度: %d 字符", deviceID, len(userProfile))
			err = managerAPI.SaveDeviceMemory(ctx, deviceID, userProfile)
			if err != nil {
				log.Warnf("保存设备 %s 的用户画像失败: %v", deviceID, err)
			}
		}
	}

	ttsType := clientState.DeviceConfig.Tts.Provider
	//如果使用 xiaozhi tts，则固定使用24000hz, 20ms帧长
	if ttsType == constants.TtsTypeXiaozhi || ttsType == constants.TtsTypeEdgeOffline {
		clientState.OutputAudioFormat.SampleRate = 24000
		clientState.OutputAudioFormat.FrameDuration = 20
	}

	return clientState, nil
}

func (c *ChatManager) Start() error {
	// 启动设备状态跟踪
	if deviceStatusManager := global.GetGlobalDeviceStatusManager(); deviceStatusManager != nil {
		// 使用默认固件版本，实际版本会在设备上报时更新
		appVersion := "unknown"
		deviceStatusManager.StartSession(c.DeviceID, appVersion)
		log.Infof("设备 %s 开始状态跟踪，固件版本: %s", c.DeviceID, appVersion)
	}

	return c.session.Start(c.ctx)
}

// 主动关闭断开连接
func (c *ChatManager) Close() error {
	log.Infof("主动关闭断开连接, 设备 %s", c.clientState.DeviceID)

	// 结束设备状态跟踪
	if deviceStatusManager := global.GetGlobalDeviceStatusManager(); deviceStatusManager != nil {
		deviceStatusManager.EndSession(c.DeviceID)
		log.Infof("设备 %s 结束状态跟踪", c.DeviceID)
	}

	// 先关闭会话级别的资源
	if c.session != nil {
		c.session.Close()
	}

	// 最后取消管理器级别的上下文
	c.cancel()

	return nil
}

func (c *ChatManager) OnClose(deviceId string) {
	log.Infof("设备 %s 断开连接", deviceId)

	// 结束设备状态跟踪
	if deviceStatusManager := global.GetGlobalDeviceStatusManager(); deviceStatusManager != nil {
		deviceStatusManager.EndSession(deviceId)
		log.Infof("设备 %s 断开连接，结束状态跟踪", deviceId)
	}

	c.cancel()
	return
}

func (c *ChatManager) GetClientState() *ClientState {
	return c.clientState
}

func (c *ChatManager) GetDeviceId() string {
	return c.clientState.DeviceID
}

// getDeviceConfigWithManagerAPI 通过manager-api获取设备特定的AI模型配置
func getDeviceConfigWithManagerAPI(ctx context.Context, managerAPIService manager_api.ManagerAPIService, deviceID string, fallbackProvider userconfig.UserConfigProvider) (utypes.UConfig, error) {
	// 注意：这里需要从设备连接或配置中获取clientID和selectedModule
	clientID := deviceID                            // 生成或获取客户端ID
	selectedModules := getSelectedModules(deviceID) // 获取设备选择的模块

	defaultConfig, err := fallbackProvider.GetUserConfig(ctx, deviceID)
	if err != nil {
		log.Warnf("获取本地配置失败: %v", err)
	}
	agentConfig, err := managerAPIService.GetDeviceConfig(ctx, deviceID, clientID, selectedModules)
	if err != nil || agentConfig == nil {
		log.Warnf("从manager-api获取设备配置失败，回退到本地配置: %v", err)
	} else {
		acBytes, _ := json.Marshal(agentConfig)
		log.Infof("成功通过manager-api获取设备 %s 的AI模型配置: %+v", deviceID, string(acBytes))
		// 成功获取配置，转换为UConfig格式
		uConfig, err := convertAgentConfigToUConfig(&defaultConfig, agentConfig, deviceID)
		if err != nil {
			log.Errorf("转换agent配置失败: %v", err)
		} else {
			log.Infof("成功通过manager-api获取设备 %s 的AI模型配置", deviceID)
			return uConfig, nil
		}
	}

	// 🔄 回退到原有的本地配置获取逻辑
	log.Debugf("使用本地配置提供者获取设备 %s 配置", deviceID)
	return fallbackProvider.GetUserConfig(ctx, deviceID)
}

// getSelectedModules 获取设备选择的AI模块配置
func getSelectedModules(deviceID string) map[string]string {
	// 可以从设备配置、用户偏好或默认配置中获取
	// 这里提供默认的模块选择
	return map[string]string{
		"VAD": viper.GetString("default_modules.vad"),
		"ASR": viper.GetString("default_modules.asr"),
		"LLM": viper.GetString("default_modules.llm"),
		"TTS": viper.GetString("default_modules.tts"),
	}
}

// convertAgentConfigToUConfig 将AgentModelsResponse转换为UConfig
func convertAgentConfigToUConfig(defaultConfig *utypes.UConfig, agentConfig *manager_api_types.AgentModelsConfig, deviceID string) (utypes.UConfig, error) {
	// 🔧 将manager-api返回的配置映射到现有的UConfig结构
	uConfig := defaultConfig
	systemPrompt := getSystemPromptFromConfig(agentConfig)
	if systemPrompt != "" {
		uConfig.SystemPrompt = systemPrompt
	}
	uConfig.Llm.Provider = getModelProvider("LLM", agentConfig.SelectedModule, "openai")
	uConfig.Llm.Config = getModelConfig("LLM", agentConfig.SelectedModule, agentConfig.LLM)

	uConfig.Tts.Provider = getModelProvider("TTS", agentConfig.SelectedModule, constants.TtsTypeEdge)
	uConfig.Tts.Config = getModelConfig("TTS", agentConfig.SelectedModule, agentConfig.TTS)

	uConfig.Asr.Provider = getModelProvider("ASR", agentConfig.SelectedModule, constants.AsrTypeFunAsr)
	uConfig.Asr.Config = getModelConfig("ASR", agentConfig.SelectedModule, agentConfig.ASR)

	uConfig.Memory.Provider = getModelProvider("Memory", agentConfig.SelectedModule, "none")
	uConfig.Memory.Config = getModelConfig("Memory", agentConfig.SelectedModule, agentConfig.Memory)

	uConfig.Plugins = agentConfig.Plugins

	log.Infof("设备 %s 配置转换完成: VAD=%s, ASR=%s, LLM=%s, TTS=%s TTS-CONFIG:%v Memory=%s",
		deviceID, uConfig.Vad.Provider, uConfig.Asr.Provider,
		uConfig.Llm.Provider, uConfig.Tts.Provider, uConfig.Tts.Config,
		uConfig.Memory.Provider,
	)

	return *uConfig, nil
}

// getModelProvider 获取模型提供者，如果为空则使用默认值
func getModelProvider(selectedModule string, modelConfig map[string]string, defaultProvider string) string {
	if selectedModule == "" {
		return defaultProvider
	}

	providerMap := map[string]string{
		"TTS_EdgeTTS":       constants.TtsTypeEdge,
		"TTS_DoubaoTTS":     constants.TtsTypeDoubaoWS,
		"TTS_DoubaoWSTTS":   constants.TtsTypeDoubaoWS,
		"TTS_XiaozhiTTS":    constants.TtsTypeXiaozhi,
		"TTS_CosyvoiceTTS":  constants.TtsTypeCosyvoice,
		"TTS_GPT_SOVITS_V3": constants.TtsTypeGptSovitsV3,
		"TTS_GPT_SOVITS_V2": constants.TtsTypeGptSovitsV2,

		"ASR_DoubaoStreamASR": constants.AsrTypeDoubao,
		"ASR_FunASRServer":    constants.AsrTypeFunAsr,
		"ASR_AliyunStreamASR": constants.AsrTypeAliyun,

		"Memory_nomem":           string(hybrid.NoneMemoryType),
		"default":                string(hybrid.DefaultMemoryType),
		"Memory_mem_local_short": string(hybrid.ShortTermMemoryType),
		"Memory_mem0ai":          string(hybrid.LongTermMemoryType),
	}

	if val, ok := modelConfig[selectedModule]; ok {
		if _, mapOk := providerMap[val]; mapOk {
			return providerMap[val]
		}

		switch selectedModule {
		case "TTS":
			return constants.TtsTypeSherpaOnnx
		case "ASR":
			return constants.AsrTypeFunAsr
		}
		return modelConfig[selectedModule]
	}

	return defaultProvider
}

// getModelConfig 获取模型配置，如果为空则返回空配置
func getModelConfig(selectedModule string, selectedModules map[string]string, modelConfig map[string]map[string]interface{}) map[string]interface{} {
	val, ok := modelConfig[selectedModules[selectedModule]]
	if ok {
		return val
	}
	return make(map[string]interface{})
}

// getSystemPromptFromConfig 从配置中获取系统提示词
func getSystemPromptFromConfig(agentConfig *manager_api_types.AgentModelsConfig) string {
	// 在新的结构中，system prompt 可能在 LLM 模型配置中
	if agentConfig.LLM != nil && agentConfig.Prompt != " " {
		return agentConfig.Prompt
	}
	// 使用默认系统提示词
	return viper.GetString("system_prompt")
}
