package global

import (
	"xiaozhi-esp32-server-golang/internal/manager_api"
)

// 全局设备状态管理器
var globalDeviceStatusManager *manager_api.DeviceStatusManager

// SetGlobalDeviceStatusManager 设置全局设备状态管理器
func SetGlobalDeviceStatusManager(manager *manager_api.DeviceStatusManager) {
	globalDeviceStatusManager = manager
}

// GetGlobalDeviceStatusManager 获取全局设备状态管理器
func GetGlobalDeviceStatusManager() *manager_api.DeviceStatusManager {
	return globalDeviceStatusManager
}
