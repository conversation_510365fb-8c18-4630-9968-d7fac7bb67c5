package manager_api

import (
	"context"
	"sync"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
	"xiaozhi-esp32-server-golang/logger"
)

// DeviceStatusManager 设备状态管理器
type DeviceStatusManager struct {
	client   *HTTPClient
	sessions map[string]*types.DeviceSessionTracker
	mutex    sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// NewDeviceStatusManager 创建设备状态管理器
func NewDeviceStatusManager(client *HTTPClient) *DeviceStatusManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &DeviceStatusManager{
		client:   client,
		sessions: make(map[string]*types.DeviceSessionTracker),
		ctx:      ctx,
		cancel:   cancel,
	}

	// 启动定期上报goroutine
	manager.wg.Add(1)
	go manager.periodicReportLoop()

	return manager
}

// StartSession 开始设备会话
func (dsm *DeviceStatusManager) StartSession(deviceID, appVersion string) {
	dsm.mutex.Lock()
	defer dsm.mutex.Unlock()

	// 如果已存在会话，先结束旧会话
	if existingSession, exists := dsm.sessions[deviceID]; exists {
		logger.Infof("设备 %s 已存在会话，结束旧会话", deviceID)
		existingSession.Close()
	}

	// 创建新会话
	session := types.NewDeviceSessionTracker(deviceID, appVersion)
	dsm.sessions[deviceID] = session

	logger.Infof("设备 %s 开始新会话，固件版本: %s", deviceID, appVersion)
}

// EndSession 结束设备会话并上报最终状态
func (dsm *DeviceStatusManager) EndSession(deviceID string) {
	dsm.mutex.Lock()
	session, exists := dsm.sessions[deviceID]
	if !exists {
		dsm.mutex.Unlock()
		logger.Warnf("设备 %s 会话不存在，无法结束", deviceID)
		return
	}

	// 关闭会话
	session.Close()
	sessionDuration := session.GetSessionDuration()
	appVersion := session.AppVersion

	// 从管理器中移除会话
	delete(dsm.sessions, deviceID)
	dsm.mutex.Unlock()

	// 上报最终状态
	if sessionDuration > 0 {
		req := &types.DeviceStatusReportRequest{
			DeviceID:        deviceID,
			SessionDuration: sessionDuration,
			IsOnline:        false, // 会话结束，设备离线
			AppVersion:      appVersion,
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		err := dsm.client.ReportDeviceStatus(ctx, req)
		if err != nil {
			logger.Errorf("设备 %s 会话结束时上报状态失败: %v", deviceID, err)
		} else {
			logger.Infof("设备 %s 会话结束，上报使用时长: %d秒", deviceID, sessionDuration)
		}
	}
}

// UpdateSessionActivity 更新会话活动（可选，用于记录中间活动）
func (dsm *DeviceStatusManager) UpdateSessionActivity(deviceID string) {
	dsm.mutex.RLock()
	session, exists := dsm.sessions[deviceID]
	dsm.mutex.RUnlock()

	if exists && session.IsActive {
		session.LastReport = time.Now()
	}
}

// periodicReportLoop 定期上报循环
func (dsm *DeviceStatusManager) periodicReportLoop() {
	defer dsm.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 每30秒上报一次
	defer ticker.Stop()

	for {
		select {
		case <-dsm.ctx.Done():
			logger.Infof("设备状态管理器停止定期上报")
			return
		case <-ticker.C:
			dsm.reportActiveSessions()
		}
	}
}

// reportActiveSessions 上报所有活跃会话的状态
func (dsm *DeviceStatusManager) reportActiveSessions() {
	dsm.mutex.RLock()
	activeSessions := make([]*types.DeviceSessionTracker, 0, len(dsm.sessions))
	for _, session := range dsm.sessions {
		if session.IsActive {
			activeSessions = append(activeSessions, session)
		}
	}
	dsm.mutex.RUnlock()

	if len(activeSessions) == 0 {
		return
	}

	logger.Debugf("开始定期上报 %d 个活跃设备会话状态", len(activeSessions))

	for _, session := range activeSessions {
		// 计算自上次上报以来的时长
		now := time.Now()
		duration := int64(now.Sub(session.LastReport).Seconds())

		if duration < 1 {
			continue // 跳过时长太短的上报
		}

		req := &types.DeviceStatusReportRequest{
			DeviceID:        session.DeviceID,
			SessionDuration: duration,
			IsOnline:        true,
			AppVersion:      session.AppVersion,
		}

		// 使用短超时的上下文
		ctx, cancel := context.WithTimeout(dsm.ctx, 5*time.Second)

		err := dsm.client.ReportDeviceStatus(ctx, req)
		cancel()

		if err != nil {
			logger.Errorf("设备 %s 定期上报状态失败: %v", session.DeviceID, err)
		} else {
			// 更新会话信息
			dsm.mutex.Lock()
			if currentSession, exists := dsm.sessions[session.DeviceID]; exists && currentSession.IsActive {
				currentSession.UpdateSession(duration)
				logger.Debugf("设备 %s 定期上报成功，时长: %d秒", session.DeviceID, duration)
			}
			dsm.mutex.Unlock()
		}
	}
}

// GetSessionInfo 获取设备会话信息
func (dsm *DeviceStatusManager) GetSessionInfo(deviceID string) (*types.DeviceSessionTracker, bool) {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	session, exists := dsm.sessions[deviceID]
	if !exists {
		return nil, false
	}

	// 返回会话的副本
	sessionCopy := *session
	return &sessionCopy, true
}

// GetAllActiveSessions 获取所有活跃会话信息
func (dsm *DeviceStatusManager) GetAllActiveSessions() map[string]*types.DeviceSessionTracker {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	result := make(map[string]*types.DeviceSessionTracker)
	for deviceID, session := range dsm.sessions {
		if session.IsActive {
			sessionCopy := *session
			result[deviceID] = &sessionCopy
		}
	}

	return result
}

// Close 关闭设备状态管理器
func (dsm *DeviceStatusManager) Close() {
	logger.Infof("正在关闭设备状态管理器...")

	// 结束所有活跃会话
	dsm.mutex.Lock()
	for deviceID := range dsm.sessions {
		logger.Infof("结束设备 %s 的会话", deviceID)
	}
	activeSessions := make([]string, 0, len(dsm.sessions))
	for deviceID := range dsm.sessions {
		activeSessions = append(activeSessions, deviceID)
	}
	dsm.mutex.Unlock()

	// 逐个结束会话
	for _, deviceID := range activeSessions {
		dsm.EndSession(deviceID)
	}

	// 停止定期上报
	dsm.cancel()
	dsm.wg.Wait()

	logger.Infof("设备状态管理器已关闭")
}

// GetStats 获取管理器统计信息
func (dsm *DeviceStatusManager) GetStats() map[string]interface{} {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_sessions":  len(dsm.sessions),
		"active_sessions": 0,
		"devices":         make([]string, 0, len(dsm.sessions)),
	}

	activeCount := 0
	devices := make([]string, 0, len(dsm.sessions))

	for deviceID, session := range dsm.sessions {
		devices = append(devices, deviceID)
		if session.IsActive {
			activeCount++
		}
	}

	stats["active_sessions"] = activeCount
	stats["devices"] = devices

	return stats
}
